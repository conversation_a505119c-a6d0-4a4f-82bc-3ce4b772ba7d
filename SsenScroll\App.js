import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as NavigationBar from 'expo-navigation-bar';
import AppNavigator from './navigation/AppNavigator';

export default function App() {
  useEffect(() => {
    // Set bottom nav bar color to match status bar
    NavigationBar.setBackgroundColorAsync('#1a1a1a');
    NavigationBar.setButtonStyleAsync('light');
  }, []);

  return (
    <SafeAreaProvider>
      <AppNavigator />
      <StatusBar style="light"  />
    </SafeAreaProvider>
  );
}
