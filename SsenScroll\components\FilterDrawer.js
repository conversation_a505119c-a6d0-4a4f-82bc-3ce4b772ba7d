import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View, Text, StyleSheet, TouchableOpacity, ScrollView,
  SafeAreaView, Modal, ActivityIndicator, Alert, TextInput, StatusBar // <-- StatusBar is still good to have!
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// import * as NavigationBar from 'expo-navigation-bar'; // <-- REMOVE THIS
import { comickAPI } from '../services/api';

// --- (Constants and Sub-components are unchanged) ---
const TIME_OPTIONS = [
  { label: 'All Time', value: 5000 },
  { label: '3 Months', value: 90 },
  { label: '6 Months', value: 180 },
  { label: '1 Year', value: 365 },
  { label: '2 Years', value: 730 },
];

const SORT_OPTIONS = [
    { label: 'Popularity', value: 'follow' },
    { label: 'Rating', value: 'rating' },
    { label: 'Views', value: 'view' },
    { label: 'Newest', value: 'created_at' },
    { label: 'Last Updated', value: 'uploaded' },
];

const FilterOption = ({ label, isSelected, onPress }) => (
  <TouchableOpacity
    style={[styles.chip, isSelected && styles.chipSelected]}
    onPress={onPress}
  >
    <Text style={[styles.chipText, isSelected && styles.chipTextSelected]}>
      {label}
    </Text>
  </TouchableOpacity>
);

const SelectedTagChip = ({ label, onRemove }) => (
    <TouchableOpacity style={styles.selectedTagChip} onPress={onRemove}>
        <Text style={styles.selectedTagChipText}>{label}</Text>
        <Ionicons name="close-circle" size={18} color="#1F1D2B" style={{ marginLeft: 6 }} />
    </TouchableOpacity>
);

const FilterSection = ({ title, children }) => (
    <View style={styles.section}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <View style={styles.chipContainer}>{children}</View>
    </View>
);

// --- Main Drawer Component ---

// <-- Accept onShow in props
export const FilterDrawer = ({ isVisible, onClose, onShow, onApply, activeFilters }) => {
  // Data state
  const [genres, setGenres] = useState([]);
  const [tags, setTags] = useState([]); // All available tags
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Filter selection state
  const [selectedGenres, setSelectedGenres] = useState(activeFilters.genres || []);
  const [selectedTags, setSelectedTags] = useState([]); // Always an array of objects { slug, name }
  const [selectedTime, setSelectedTime] = useState(activeFilters.time || null);
  const [selectedSort, setSelectedSort] = useState(activeFilters.sort || 'follow');

  // Tag search state
  const [tagSearchQuery, setTagSearchQuery] = useState('');
  const [tagSearchResults, setTagSearchResults] = useState([]);
  const [isTagSearching, setIsTagSearching] = useState(false);
  const debounceTimeout = useRef(null);

  // --- Effects ---
  
  // REMOVE the useEffect for NavigationBar that we added before.

  // Effect to fetch initial data
  useEffect(() => {
    if (isVisible && genres.length === 0 && tags.length === 0 && !error) {
      fetchInitialData();
    }
  }, [isVisible]);

  // Effect to sync state with activeFilters prop
  useEffect(() => {
    if (isVisible) {
      setSelectedGenres(activeFilters.genres || []);
      setSelectedTime(activeFilters.time || null);
      setSelectedSort(activeFilters.sort || 'follow');

      if (tags.length > 0) {
        const activeTagSlugs = activeFilters.tags || [];
        const correspondingTagObjects = activeTagSlugs
          .map(slug => tags.find(tagInList => tagInList.slug === slug))
          .filter(Boolean); 
        setSelectedTags(correspondingTagObjects);
      }
    }
  }, [isVisible, activeFilters, tags]); 

  // --- (All other functions like fetchInitialData, handleTagSearch, etc. are unchanged) ---
  const fetchInitialData = async () => {
    setLoading(true);
    setError(null);
    try {
      const [genreData, tagData] = await Promise.all([
        comickAPI.getGenres(),
        comickAPI.getTags()
      ]);
      const sortedGenres = genreData?.filter(g => g.name && g.slug).sort((a, b) => a.name.localeCompare(b.name)) || [];
      const sortedTags = tagData?.filter(t => t.name && t.slug).sort((a, b) => a.name.localeCompare(b.name)) || [];
      setGenres(sortedGenres);
      setTags(sortedTags);
    } catch (e) {
      setError("Could not load filters. Please try again later.");
      console.error("Failed to fetch filter data", e);
    } finally {
      setLoading(false);
    }
  };

  const handleTagSearch = (query) => {
    setTagSearchQuery(query);
    if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
    if (!query.trim()) {
      setTagSearchResults([]);
      setIsTagSearching(false);
      return;
    }
    setIsTagSearching(true);
    debounceTimeout.current = setTimeout(async () => {
      try {
        const results = await comickAPI.searchTags(query);
        const formattedResults = results.map(r => ({ slug: r.k, name: r.v.replace(/\s\(\d+\)$/, '') }));
        setTagSearchResults(formattedResults);
      } catch (e) {
        console.error("Failed to search tags", e);
        setTagSearchResults([]);
      } finally {
        setIsTagSearching(false);
      }
    }, 300);
  };

  const handleToggleGenre = (genreSlug) => {
    setSelectedGenres((prev) =>
      prev.includes(genreSlug) ? prev.filter((g) => g !== genreSlug) : [...prev, genreSlug]
    );
  };

  const handleSelectTag = (tag) => {
    if (!selectedTags.find(t => t.slug === tag.slug)) {
      setSelectedTags(prev => [...prev, tag]);
    }
    setTagSearchQuery('');
    setTagSearchResults([]);
  };

  const handleRemoveTag = (tagSlug) => {
    setSelectedTags(prev => prev.filter(t => t.slug !== tagSlug));
  };

  const handleApply = () => {
    if (selectedGenres.length === 0 && selectedTags.length === 0) {
      Alert.alert("No Filters Selected", "Please select at least one genre or tag to see results.");
      return;
    }
    onApply({
      genres: selectedGenres,
      tags: selectedTags.map(t => t.slug),
      time: selectedTime,
      sort: selectedSort
    });
  };

  const handleReset = () => {
    setSelectedGenres([]);
    setSelectedTags([]);
    setSelectedTime(null);
    setSelectedSort('follow');
    setTagSearchQuery('');
    setTagSearchResults([]);
    onApply({ genres: [], tags: [], time: null, sort: null });
  };


  // --- Render Methods ---

  const renderContent = () => {
    if (loading) {
      return <ActivityIndicator style={styles.loader} size="large" color="#FFF" />;
    }
    if (error) {
      return <View style={styles.errorContainer}><Text style={styles.errorText}>{error}</Text></View>;
    }
    const totalFilters = selectedGenres.length + selectedTags.length;

    return (
      <>
        <ScrollView style={{ flex: 1 }} contentContainerStyle={styles.scrollViewContent}>
          <FilterSection title="Sort By">
            {SORT_OPTIONS.map((opt) => (
              <FilterOption key={opt.value} label={opt.label} isSelected={selectedSort === opt.value} onPress={() => setSelectedSort(opt.value)} />
            ))}
          </FilterSection>

          <FilterSection title="Updated In">
            {TIME_OPTIONS.map((opt) => (
              <FilterOption key={opt.label} label={opt.label} isSelected={selectedTime === opt.value} onPress={() => setSelectedTime(opt.value)} />
            ))}
          </FilterSection>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Tags</Text>
            <View style={styles.tagSearchContainer}>
              <Ionicons name="search" size={20} color="#8A8899" style={styles.tagSearchIcon} />
              <TextInput
                style={styles.tagSearchInput}
                placeholder="Search for tags (e.g., Post-Apocalyptic)"
                placeholderTextColor="#8A8899"
                value={tagSearchQuery}
                onChangeText={handleTagSearch}
              />
            </View>

            {selectedTags.length > 0 && (
              <View style={styles.chipContainer}>
                {selectedTags.map(tag => (
                  <SelectedTagChip key={tag.slug} label={tag.name} onRemove={() => handleRemoveTag(tag.slug)} />
                ))}
              </View>
            )}

            {isTagSearching ? (
              <ActivityIndicator style={{ marginTop: 15 }} size="small" color="#FFF" />
            ) : (
              tagSearchResults.length > 0 && (
                <View style={[styles.chipContainer, { marginTop: 10 }]}>
                  {tagSearchResults.map(tag => (
                    !selectedTags.some(st => st.slug === tag.slug) && (
                      <FilterOption key={tag.slug} label={tag.name} isSelected={false} onPress={() => handleSelectTag(tag)} />
                    )
                  ))}
                </View>
              )
            )}
          </View>

          <FilterSection title="Genres">
            {genres.map((genre) => (
              <FilterOption key={genre.slug} label={genre.name} isSelected={selectedGenres.includes(genre.slug)} onPress={() => handleToggleGenre(genre.slug)} />
            ))}
          </FilterSection>
        </ScrollView>
        <View style={styles.footer}>
          <TouchableOpacity style={styles.resetButton} onPress={handleReset}><Text style={styles.resetButtonText}>Reset</Text></TouchableOpacity>
          <TouchableOpacity style={styles.applyButton} onPress={handleApply}><Text style={styles.applyButtonText}>Apply Filters ({totalFilters})</Text></TouchableOpacity>
        </View>
      </>
    );
  };

  return (
    <Modal
        animationType="slide"
        transparent={true}
        visible={isVisible}
        onRequestClose={onClose}
        onShow={onShow} // <-- Pass the prop here
    >
      <StatusBar barStyle="light-content" />
      <TouchableOpacity style={styles.modalBackdrop} activeOpacity={1} onPress={onClose} />
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.drawerHeader}>
          <Text style={styles.drawerTitle}>Filters</Text>
          {/* This now calls the new handler from SearchScreen */}
          <TouchableOpacity onPress={onClose}><Text style={styles.doneButton}>Done</Text></TouchableOpacity>
        </View>
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
};


// --- (Styles are unchanged) ---
const styles = StyleSheet.create({
  modalBackdrop: { flex: 1},
  modalContainer: { position: 'absolute', bottom: 0, left: 0, right: 0, height: '85%', backgroundColor: '#0F0F12', borderTopLeftRadius: 20, borderTopRightRadius: 20 },
  drawerHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: 20, borderBottomWidth: 1, borderBottomColor: 'rgba(255,255,255,0.05)' },
  drawerTitle: { color: '#FFF', fontSize: 22, fontWeight: 'bold' },
  doneButton: { color: '#A39DCE', fontSize: 16, fontWeight: '600' },
  loader: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  scrollViewContent: { paddingHorizontal: 20, paddingBottom: 20 },
  section: { marginTop: 25 },
  sectionTitle: { color: '#FFF', fontSize: 18, fontWeight: 'bold', marginBottom: 15 },
  chipContainer: { flexDirection: 'row', flexWrap: 'wrap' },
  chip: { backgroundColor: 'rgba(30,30,36,0.6)', borderWidth: 1, borderColor: 'rgba(255,255,255,0.05)', paddingVertical: 10, paddingHorizontal: 18, borderRadius: 20, marginRight: 10, marginBottom: 10 },
  chipSelected: { backgroundColor: '#A39DCE', borderColor: '#A39DCE' },
  chipText: { color: '#D0CFD4', fontSize: 14, fontWeight: '500' },
  chipTextSelected: { color: '#0F0F12', fontWeight: 'bold' },
  tagSearchContainer: { flexDirection: 'row', alignItems: 'center', backgroundColor: 'rgba(30,30,36,0.6)', borderWidth: 1, borderColor: 'rgba(255,255,255,0.05)', borderRadius: 20, paddingHorizontal: 15, marginBottom: 15 },
  tagSearchIcon: { marginRight: 10 },
  tagSearchInput: { flex: 1, height: 45, color: '#FFF', fontSize: 14 },
  selectedTagChip: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#A39DCE', paddingVertical: 8, paddingHorizontal: 12, borderRadius: 16, marginRight: 10, marginBottom: 10 },
  selectedTagChipText: { color: '#0F0F12', fontSize: 14, fontWeight: '600' },
  footer: { flexDirection: 'row', padding: 20, borderTopWidth: 1, borderTopColor: 'rgba(255,255,255,0.05)', backgroundColor: '#0F0F12' },
  resetButton: { flex: 1, backgroundColor: 'rgba(30,30,36,0.6)', borderWidth: 1, borderColor: 'rgba(255,255,255,0.05)', padding: 15, borderRadius: 30, alignItems: 'center', marginRight: 10 },
  resetButtonText: { color: '#FFF', fontSize: 16, fontWeight: 'bold' },
  applyButton: { flex: 2, backgroundColor: '#A39DCE', padding: 15, borderRadius: 30, alignItems: 'center' },
  applyButtonText: { color: '#0F0F12', fontSize: 16, fontWeight: 'bold' },
  errorContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  errorText: { color: '#FF7575', fontSize: 16 },
});