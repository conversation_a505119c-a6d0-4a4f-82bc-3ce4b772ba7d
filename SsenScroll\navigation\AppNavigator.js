import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
// REMOVED: createBottomTabNavigator is no longer needed.
// import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
// REMOVED: Ionicons is no longer used in this file.
// import { Ionicons } from '@expo/vector-icons';

// Import all necessary screens
import HomeScreen from '../screens/HomeScreen';
import SearchScreen from '../screens/SearchScreen';
import FavoritesScreen from '../screens/FavoritesScreen';
import ManhwaDetailScreen from '../screens/ManhwaDetailScreen';
import ChapterReaderScreen from '../screens/ChapterReaderScreen';
import NotificationsScreen from '../screens/NotificationsScreen';

// A single Stack Navigator will now control the entire app flow.
const Stack = createStackNavigator();

// REMOVED: The old HomeStack, SearchStack, and TabNavigator functions are gone.

// --- The New, Simplified Main App Navigator ---
export default function AppNavigator() {
  return (
    <NavigationContainer>
      {/*
        The entire app is now wrapped in a single StackNavigator.
        The `initialRouteName` prop can be used to set the first screen.
        All screens are on the same level, making navigation between them simple.
      */}
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{ headerShown: false }}
      >
        <Stack.Screen name="Home" component={HomeScreen} />
        <Stack.Screen name="Search" component={SearchScreen} />
        <Stack.Screen name="Favorites" component={FavoritesScreen} />
        <Stack.Screen name="Notifications" component={NotificationsScreen} />
        <Stack.Screen name="ManhwaDetail" component={ManhwaDetailScreen} />
        <Stack.Screen name="ChapterReader" component={ChapterReaderScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}