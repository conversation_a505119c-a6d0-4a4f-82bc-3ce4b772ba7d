{"name": "ssenscroll", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "axios": "^1.11.0", "expo": "~53.0.20", "expo-image": "~2.4.0", "expo-linear-gradient": "~14.1.5", "expo-navigation-bar": "~4.2.7", "expo-status-bar": "~2.2.3", "qs": "^6.14.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}