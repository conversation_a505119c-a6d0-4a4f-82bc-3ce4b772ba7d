import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Dimensions,
  StatusBar,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
  Pressable,
  Platform,
  Switch,
  PanResponder,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import * as NavigationBar from 'expo-navigation-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import Animated, { useSharedValue, useAnimatedStyle, withSpring, withTiming, runOnJS, FadeIn } from 'react-native-reanimated';
import { comickAPI, getChapterImageUrl } from '../services/api';
import { storageService } from '../services/storageService';

const { width } = Dimensions.get('window');
const EXPANDED_TOAST_HEIGHT = 100;
const MINIMIZED_TOAST_HEIGHT = 45;

// --- Reusable UI Components ---

const MemoizedImage = React.memo(({ uri, height }) => (
  <Image
    source={{ uri }}
    style={[styles.chapterImage, { height }]}
    contentFit="contain"
    placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
    cachePolicy="disk"
    transition={50}
  />
));

const CustomToast = ({ message, onDismiss, isVisible }) => {
    const insets = useSafeAreaInsets();
    const translateY = useSharedValue(-150);
    const pan = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => true,
            onMoveShouldSetPanResponder: (_, g) => Math.abs(g.dy) > 5 || Math.abs(g.dx) > 5,
            onPanResponderRelease: (_, g) => {
                if (g.dy < -20 || g.dx < -50 || g.dx > 50) {
                     translateY.value = withTiming(-150, { duration: 200 }, (finished) => {
                         if (finished) runOnJS(onDismiss)();
                     });
                }
            },
        })
    ).current;

    useEffect(() => {
        let timer;
        if (isVisible) {
            translateY.value = withSpring(0, { damping: 18, stiffness: 120 });
            timer = setTimeout(() => {
                translateY.value = withTiming(-150, { duration: 300 }, (finished) => {
                    if (finished) runOnJS(onDismiss)();
                });
            }, 3000);
        }
        return () => clearTimeout(timer);
    }, [isVisible, onDismiss, translateY]);

    const animatedStyle = useAnimatedStyle(() => ({ transform: [{ translateY: translateY.value }] }));
    const handleDismissPress = () => {
        translateY.value = withTiming(-150, { duration: 200 }, (finished) => {
            if (finished) runOnJS(onDismiss)();
        });
    };
    
    if (!isVisible) return null;

    return (
        <Animated.View style={[styles.toastBaseContainer, {top: insets.top + 10}, animatedStyle]} {...pan.panHandlers}>
            <View style={styles.toastPill}>
                <Ionicons name="information-circle-outline" size={22} color="#FFF" style={{marginRight: 10}} />
                <Text style={styles.toastMessage} numberOfLines={2}>{message}</Text>
                 <TouchableOpacity style={styles.toastCloseButton} onPress={handleDismissPress}>
                    <Ionicons name="close" size={20} color="#EAEAEA" />
                </TouchableOpacity>
            </View>
        </Animated.View>
    );
};

const ProgressToast = ({ isVisible, statusText, progress, etaText }) => {
    const insets = useSafeAreaInsets();
    const [isMinimized, setIsMinimized] = useState(false);
    const translateY = useSharedValue(-150);
    const toastHeight = useSharedValue(EXPANDED_TOAST_HEIGHT);

    const toggleMinimize = () => {
        setIsMinimized(prev => !prev);
        toastHeight.value = withSpring(isMinimized ? EXPANDED_TOAST_HEIGHT : MINIMIZED_TOAST_HEIGHT, { damping: 15, stiffness: 150 });
    };

    useEffect(() => {
        if (isVisible) {
            translateY.value = withSpring(0, { damping: 18, stiffness: 120 });
        } else {
            setTimeout(() => {
                translateY.value = withTiming(-150, { duration: 300 });
            }, 500);
        }
    }, [isVisible]);

    const animatedContainerStyle = useAnimatedStyle(() => ({
        transform: [{ translateY: translateY.value }],
        height: toastHeight.value,
    }));
    
    const animatedProgressStyle = useAnimatedStyle(() => ({
        width: `${progress * 100}%`,
    }));

    if (!isVisible && translateY.value === -150) return null;

    return (
        <Animated.View style={[styles.progressToastContainer, { top: insets.top + 10 }, animatedContainerStyle]}>
            <Pressable style={styles.progressToastPill} onPress={toggleMinimize}>
                <View style={styles.progressToastHeader}>
                    <Ionicons 
                        name={isMinimized ? "chevron-down-circle-outline" : "chevron-up-circle-outline"} 
                        size={24} 
                        color="#EAEAEA"
                    />
                    <Text style={styles.progressStatusText} numberOfLines={1}>
                        {isMinimized ? `Colorizing... (${Math.round(progress * 100)}%)` : statusText}
                    </Text>
                </View>

                {!isMinimized && (
                     <>
                        <View style={styles.progressBarBackground}>
                            <Animated.View style={[styles.progressBarForeground, animatedProgressStyle]} />
                        </View>
                        <Text style={styles.progressEtaText}>{etaText}</Text>
                     </>
                )}
            </Pressable>
        </Animated.View>
    );
};


// --- Comment Components & Helpers ---

const formatRelativeTime = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffSeconds = Math.round((now - date) / 1000);

  if (diffSeconds < 60) return `${diffSeconds}s ago`;
  const diffMinutes = Math.round(diffSeconds / 60);
  if (diffMinutes < 60) return `${diffMinutes}m ago`;
  const diffHours = Math.round(diffMinutes / 60);
  if (diffHours < 24) return `${diffHours}h ago`;
  const diffDays = Math.round(diffHours / 24);
  if (diffDays < 7) return `${diffDays}d ago`;
  const diffWeeks = Math.round(diffDays / 7);
  if (diffWeeks <= 4) return `${diffWeeks}w ago`;
  
  return date.toLocaleDateString();
};

const parseCommentContent = (content = '') => {
  const parts = [];
  let lastIndex = 0;
  // Updated regex to find both Markdown images AND HTML <img> tags.
  // The URL is captured in either the first or second group.
  const regex = /!\[[^\]]*\]\(([^)]+)\)|<img[^>]+src="([^"]+)"/g;
  let match;

  while ((match = regex.exec(content)) !== null) {
    // Add the plain text that comes before this image match.
    if (match.index > lastIndex) {
      parts.push({ type: 'text', content: content.substring(lastIndex, match.index) });
    }
    
    // The image URI will be in one of the two capture groups.
    const uri = match[1] || match[2];
    if (uri) {
      parts.push({ type: 'image', uri });
    }
    
    // Update the index to continue searching from the end of the last match.
    lastIndex = regex.lastIndex;
  }

  // Add any remaining text after the last image.
  if (lastIndex < content.length) {
    parts.push({ type: 'text', content: content.substring(lastIndex) });
  }

  // Final step: clean up any leftover HTML tags (like <a>, <div>) from the text parts.
  return parts.map(part => {
    if (part.type === 'text') {
      const cleanedContent = part.content.replace(/<\/?[^>]+(>|$)/g, "");
      return { ...part, content: cleanedContent };
    }
    return part;
  });
};

const RenderCommentContent = React.memo(({ content }) => {
  const contentParts = useMemo(() => parseCommentContent(content), [content]);

  return (
    <View>
      {contentParts.map((part, index) => {
        if (part.type === 'text' && part.content.trim()) {
          return <Text key={`text-${index}`} style={styles.commentContent}>{part.content}</Text>;
        }
        if (part.type === 'image') {
          return (
            <Image
              key={`image-${index}`}
              source={{ uri: part.uri }}
              style={styles.commentImage}
              contentFit="contain"
              placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
              transition={200}
            />
          );
        }
        return null;
      })}
    </View>
  );
});

const MemoizedComment = React.memo(({ comment, isReply = false }) => {
    if (comment.status === 'deleted' || !comment.identities) {
        return (
            <View style={[styles.commentContainer, isReply && styles.replyContainer]}>
                <Text style={styles.deletedCommentText}>[Comment unavailable or deleted]</Text>
            </View>
        );
    }
    
    return (
        <View style={[styles.commentContainer, isReply && styles.replyContainer]}>
            <View style={styles.commentHeader}>
                <Image 
                    source={{ uri: comment.identities.traits.gravatar }} 
                    style={styles.commentAvatar} 
                />
                <View style={styles.commentHeaderInfo}>
                    <Text style={styles.commentUsername} numberOfLines={1}>{comment.identities.traits.username}</Text>
                    <Text style={styles.commentDate}>{formatRelativeTime(comment.created_at)}</Text>
                </View>
            </View>
            <RenderCommentContent content={comment.content} />
            <View style={styles.commentFooter}>
                <Ionicons name="arrow-up-outline" size={16} color="#AAA" />
                <Text style={styles.commentVotes}>{comment.up_count}</Text>
                <Ionicons name="arrow-down-outline" size={16} color="#AAA" style={{ marginLeft: 10 }}/>
                <Text style={styles.commentVotes}>{comment.down_count}</Text>
            </View>
            {comment.other_comments && comment.other_comments.length > 0 && (
                <View style={styles.repliesSection} pointerEvents="box-none">
                    {comment.other_comments.map(reply => (
                        <MemoizedComment key={reply.id} comment={reply} isReply={true} />
                    ))}
                </View>
            )}
        </View>
    );
});


export default function ChapterReaderScreen({ route, navigation }) {
  const { allChapters, currentChapter, manhwaInfo } = route.params;

  // --- State Declarations ---
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [showChapterModal, setShowChapterModal] = useState(false);
  const [showScanlatorModal, setShowScanlatorModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [currentChapterData, setCurrentChapterData] = useState(currentChapter);
  const [searchQuery, setSearchQuery] = useState('');
  const [readingProgress, setReadingProgress] = useState(null);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [chapterDetails, setChapterDetails] = useState(null);
  const [showCommentsModal, setShowCommentsModal] = useState(false);
  const [comments, setComments] = useState([]);
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [showCountdown, setShowCountdown] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [publishDate, setPublishDate] = useState(null);

  const [readerSettings, setReaderSettings] = useState({
      decelerationRate: 'normal',
      backgroundColor: '#000000',
      showProgressBar: true,
  });
  const [toastConfig, setToastConfig] = useState({ isVisible: false, message: '' });

  // --- COLORIZER --- State for the colorization feature
  const [showColorizerModal, setShowColorizerModal] = useState(false);
  const [isColorizing, setIsColorizing] = useState(false);
  const [colorizerSettings, setColorizerSettings] = useState({
    baseUrl: 'https://f5507f2ec753.ngrok-free.app',
    colorize: true,
    upscale: false,
    denoise: false,
    useBackendCache: true,
  });
  const [isChapterColorized, setIsChapterColorized] = useState(false);
  const [colorizationProgress, setColorizationProgress] = useState(0);
  const [colorizationStatusText, setColorizationStatusText] = useState('');
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState('');


  const savedScrollOffsetRef = useRef(0);
  const controlsTimeoutRef = useRef(null);
  const flatListRef = useRef(null);
  const insets = useSafeAreaInsets();
  const isScrolling = useRef(false);
  const chapterJustLoaded = useRef(true);

  // --- USEEFFECT HOOKS ---
  useEffect(() => {
    const loadSettings = async () => {
      const savedReaderSettings = await storageService.getReaderSettings(); 
      if (savedReaderSettings) setReaderSettings(prev => ({...prev, ...savedReaderSettings}));
      
      const savedColorizerSettings = await storageService.getColorizerSettings();
      if (savedColorizerSettings) setColorizerSettings(prev => ({...prev, ...savedColorizerSettings}));
    };
    loadSettings();
  }, []);

  useEffect(() => {
    storageService.saveReaderSettings(readerSettings);
  }, [readerSettings]);

  useEffect(() => {
    storageService.saveColorizerSettings(colorizerSettings);
  }, [colorizerSettings]);

  useFocusEffect(
    useCallback(() => {
      const getProgress = async () => {
        const progress = await storageService.getReadingProgress(manhwaInfo.slug);
        setReadingProgress(progress);
      };

      getProgress();
      navigation.setOptions({ headerShown: false });
      if (Platform.OS === 'android') {
        NavigationBar.setBehaviorAsync('inset-swipe');
        NavigationBar.setVisibilityAsync('hidden');
      }
      resetControlsTimeout();
      return () => {
         if (Platform.OS === 'android') NavigationBar.setVisibilityAsync('visible');
         clearTimeout(controlsTimeoutRef.current);
      }
    }, [manhwaInfo.slug])
  );

  useEffect(() => {
    if (Platform.OS === 'android' && !showControls) NavigationBar.setVisibilityAsync('hidden');
  }, [showControls]);
  
  useEffect(() => {
    if (currentChapterData?.hid) {
      let initialOffset = 0;
      if (readingProgress && readingProgress.lastChapterHid === currentChapterData.hid) {
        initialOffset = readingProgress.scrollOffset || 0;
      }
      savedScrollOffsetRef.current = initialOffset; 
      loadChapterImages(currentChapterData, initialOffset);
    }
  }, [currentChapterData, readingProgress]); 
  
  useEffect(() => {
    return () => { if (currentChapterData) saveReadingProgress(currentChapterData) };
  }, [currentChapterData]);
  
  useEffect(() => {
    if (!showCountdown || !publishDate) return;
    const calculateTime = () => {
        const difference = new Date(publishDate) - new Date();
        if (difference <= 0) {
            clearInterval(interval);
            setShowCountdown(false);
            showToast("Chapter is available! Refreshing...");
            handleRefresh();
            return;
        }
        setTimeRemaining({
            days: Math.floor(difference / (1000 * 60 * 60 * 24)),
            hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
            minutes: Math.floor((difference / 1000 / 60) % 60),
            seconds: Math.floor((difference / 1000) % 60),
        });
    };
    calculateTime();
    const interval = setInterval(calculateTime, 1000);
    return () => clearInterval(interval);
  }, [showCountdown, publishDate, currentChapterData]);

  useEffect(() => {
    if (!isColorizing) return;

    const runColorization = async () => {
        setColorizationProgress(0);
        setEstimatedTimeRemaining('');
        const totalImages = images.length;
        setColorizationStatusText(`Initializing... (0 of ${totalImages})`);
        const startTime = Date.now();

        for (let i = 0; i < totalImages; i++) {
            const image = images[i];
            const processedCount = i + 1;
            setColorizationStatusText(`Colorizing page ${processedCount} of ${totalImages}...`);

            try {
                const imageName = image.originalUri.split('/').pop();
                const payload = {
                    imgURL: image.originalUri,
                    colorize: colorizerSettings.colorize,
                    upscale: colorizerSettings.upscale,
                    denoise: colorizerSettings.denoise,
                    mangaTitle: manhwaInfo.title,
                    mangaChapter: currentChapterData.chap,
                    imgName: imageName,
                    cache: colorizerSettings.useBackendCache,
                };

                const response = await fetch(`${colorizerSettings.baseUrl}/colorize-image-data`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload),
                });

                if (!response.ok) throw new Error(`API Error: ${response.status}`);

                const result = await response.json();
                if (result.colorImgData) {
                    setImages(prevImages => {
                        const updatedImages = [...prevImages];
                        if (updatedImages[i]) {
                           updatedImages[i] = { ...updatedImages[i], uri: result.colorImgData };
                        }
                        return updatedImages;
                    });
                } else {
                    throw new Error(result.msg || "Colorization failed, no image data returned.");
                }
            } catch (error) {
                console.error(`Failed to colorize image ${processedCount}:`, error);
                runOnJS(showToast)(`Page ${processedCount} failed. Check server.`);
            }

            setColorizationProgress(processedCount / totalImages);
            const elapsedTime = (Date.now() - startTime) / 1000;
            const avgTimePerImage = elapsedTime / processedCount;
            const remainingImages = totalImages - processedCount;
            const estimatedSeconds = remainingImages * avgTimePerImage;
            setEstimatedTimeRemaining(formatSeconds(estimatedSeconds));
        }
        
        setIsChapterColorized(true);
        setIsColorizing(false);
        showToast("Colorization complete!");
    };
    
    runColorization();
  }, [isColorizing]);

  // --- UI AND DATA LOGIC ---
  const showToast = useCallback((message) => setToastConfig({ isVisible: true, message }), []);
  const dismissToast = useCallback(() => setToastConfig(config => ({ ...config, isVisible: false })), []);
  
  const resetControlsTimeout = useCallback(() => {
    clearTimeout(controlsTimeoutRef.current);
    controlsTimeoutRef.current = setTimeout(() => setShowControls(false), 5000);
  }, []);

  const toggleControls = useCallback(() => {
    setShowControls(prev => {
      const isNowVisible = !prev;
      if (isNowVisible) resetControlsTimeout();
      else clearTimeout(controlsTimeoutRef.current);
      return isNowVisible;
    });
  }, [resetControlsTimeout]);

  // --- DATA FETCHING & NAVIGATION ---
  
  const { uniqueChapters, availableScanlators } = useMemo(() => {
    if (!allChapters) return { uniqueChapters: [], availableScanlators: [] };
    const englishChapters = allChapters.filter(ch => ch.lang === 'en');
    const chapterMap = new Map();
    englishChapters.forEach(ch => { if (!chapterMap.has(ch.chap)) chapterMap.set(ch.chap, ch) });
    const unique = Array.from(chapterMap.values()).sort((a, b) => parseFloat(b.chap) - parseFloat(a.chap));
    const scanlators = currentChapterData ? allChapters.filter(ch => ch.chap === currentChapterData.chap && ch.lang === 'en') : [];
    return { uniqueChapters: unique, availableScanlators: scanlators };
  }, [allChapters, currentChapterData]);

  const filteredChapters = useMemo(() =>
    uniqueChapters.filter(chapter =>
      chapter.chap?.includes(searchQuery) || chapter.title?.toLowerCase().includes(searchQuery.toLowerCase())
    ), [searchQuery, uniqueChapters]);

  const saveReadingProgress = async (chapterData) => {
    if (!manhwaInfo || !chapterData) return;
    try {
      await storageService.updateReadingProgress(manhwaInfo.slug, {
        lastChapterHid: chapterData.hid, lastChapterNumber: chapterData.chap,
        lastChapterTitle: chapterData.title || `Chapter ${chapterData.chap}`,
        highestChapterNumber: uniqueChapters?.[0]?.chap,
        manhwaTitle: manhwaInfo.title, manhwaCover: manhwaInfo.cover,
        scrollOffset: savedScrollOffsetRef.current, 
      });
    } catch (error) { console.error('Error saving reading progress:', error); }
  };
  
  const processChapterImages = (chapterDetails) => {
    const originalImages = chapterDetails?.chapter?.md_images || [];
    let cumulativeHeight = 0;
    
    return originalImages.map((img) => {
        const imageHeight = (img.h && img.w > 0) ? (img.h / img.w) * width : width * 1.5;
        const imageOffset = cumulativeHeight;
        cumulativeHeight += imageHeight;
        
        return { 
          uri: getChapterImageUrl(img), 
          originalUri: getChapterImageUrl(img),
          height: imageHeight, 
          offset: imageOffset 
        };
    });
  };

  const loadChapterImages = async (chapterData, scrollOffset) => {
    chapterJustLoaded.current = true;
    setLoading(true);
    setShowCountdown(false);
    setImages([]);
    setChapterDetails(null);
    setIsChapterColorized(false);
    try {
      const details = await comickAPI.getChapterDetails(chapterData.hid);
      setChapterDetails(details);
      
      const publishAt = details?.chapter?.publish_at;
      const originalImages = details?.chapter?.md_images || [];

      if (originalImages.length === 0 && publishAt && new Date(publishAt) > new Date()) {
          setPublishDate(publishAt);
          setShowCountdown(true);
      } else if (originalImages.length > 0) {
        const processedImages = processChapterImages(details);
        setImages(processedImages);
        if (scrollOffset > 0) {
          setTimeout(() => flatListRef.current?.scrollToOffset({ offset: scrollOffset, animated: false }), 150);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load chapter details.');
      console.error(error);
    } finally {
      setLoading(false);
      setTimeout(() => chapterJustLoaded.current = false, 500);
    }
  };
  
  const handleRefresh = async () => {
    showToast('Refreshing chapter...');
    await loadChapterImages(currentChapterData, savedScrollOffsetRef.current);
  };
  
  const handleRevertToOriginal = async () => {
      setShowColorizerModal(false);
      showToast("Reverting to original images...");
      await loadChapterImages(currentChapterData, 0);
  };

    const formatSeconds = (totalSeconds) => {
        if (isNaN(totalSeconds) || totalSeconds <= 0) {
            return 'Calculating...';
        }
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = Math.floor(totalSeconds % 60);

        if (minutes > 0) {
            return `About ${minutes}m ${seconds}s remaining`;
        }
        return `About ${seconds}s remaining`;
    };

    const handleStartColorization = () => {
        setShowColorizerModal(false);
        if (!colorizerSettings.baseUrl) {
            Alert.alert("Error", "Please set the Colorizer Base URL in the settings.");
            return;
        }
        setIsColorizing(true);
    };


  const resetChapterState = () => {
    setShowCommentsModal(false);
    setComments([]);
    setChapterDetails(null);
    savedScrollOffsetRef.current = 0;
    setShowCountdown(false);
    setPublishDate(null);
    setIsChapterColorized(false);
  };
  
  const handleNavigation = (direction) => {
      saveReadingProgress(currentChapterData); 
      resetChapterState(); 
      const currentIndex = uniqueChapters.findIndex(ch => ch.hid === currentChapterData.hid);
      const newIndex = direction === 'next' ? currentIndex - 1 : currentIndex + 1;
      if (newIndex >= 0 && newIndex < uniqueChapters.length) {
          setCurrentChapterData(uniqueChapters[newIndex]);
      }
  };

  const selectChapter = (chapter) => { 
    if(chapter.hid !== currentChapterData.hid) {
      saveReadingProgress(currentChapterData);
      resetChapterState();
      setCurrentChapterData(chapter); 
      setShowChapterModal(false);
    }
  };

  const selectScanlator = (chapter) => {
    if(chapter.hid !== currentChapterData.hid) {
      saveReadingProgress(currentChapterData);
      resetChapterState();
      setCurrentChapterData(chapter); 
      setShowScanlatorModal(false);
    }
  };
  
  const updateSetting = (key, value) => setReaderSettings(prev => ({ ...prev, [key]: value }));
  const updateColorizerSetting = (key, value) => setColorizerSettings(prev => ({ ...prev, [key]: value }));

  // In ChapterReaderScreen.js

  const handleOpenComments = async () => {
    // ADD THIS CHECK
    

    setShowCommentsModal(true);
    if (comments.length > 0 || commentsLoading) return;

    setCommentsLoading(true);
    try {
      const data = await comickAPI.getChapterComments({
        hid: currentChapterData.id,
        comicId: manhwaInfo.hid, // This now uses the verified manhwaInfo.hid
        chap: currentChapterData.chap,
      });
      setComments(data?.comments?.filter(c => c && c.id) || []);
    } catch (error) {
      console.error("Failed to fetch comments:", error);
      showToast("Could not load comments for this chapter.");
      setShowCommentsModal(false);
    } finally {
      setCommentsLoading(false);
    }
  };

  const renderImage = useCallback(({ item }) => <MemoizedImage uri={item.uri} height={item.height} />, []);
  const getItemLayout = useCallback((data, index) => ({ length: data[index].height, offset: data[index].offset, index }), []);
  
  const onScroll = useCallback((event) => {
    if(chapterJustLoaded.current) return;
    const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent;
    savedScrollOffsetRef.current = contentOffset.y;
    if (readerSettings.showProgressBar && contentSize.height > 0) {
      setScrollProgress(Math.min(1, (contentOffset.y + layoutMeasurement.height) / contentSize.height));
    }
  }, [readerSettings.showProgressBar]);

  const renderCountdown = () => (
    <Animated.View style={styles.countdownContainer} entering={FadeIn.duration(300)}>
      <View style={styles.countdownContent}>
        <Ionicons name="timer-outline" size={40} color="#FF6B6B" style={{ opacity: 0.9 }} />
        <Text style={styles.countdownHeader}>CHAPTER RELEASING IN</Text>
        <View style={styles.timerDisplay}>
          <View style={styles.timeBlock}><Text style={styles.timeNumber}>{String(timeRemaining.days).padStart(2, '0')}</Text><Text style={styles.timeLabel}>DAYS</Text></View>
          <Text style={styles.timeSeparator}>:</Text>
          <View style={styles.timeBlock}><Text style={styles.timeNumber}>{String(timeRemaining.hours).padStart(2, '0')}</Text><Text style={styles.timeLabel}>HOURS</Text></View>
          <Text style={styles.timeSeparator}>:</Text>
          <View style={styles.timeBlock}><Text style={styles.timeNumber}>{String(timeRemaining.minutes).padStart(2, '0')}</Text><Text style={styles.timeLabel}>MINUTES</Text></View>
          <Text style={styles.timeSeparator}>:</Text>
          <View style={styles.timeBlock}><Text style={styles.timeNumber}>{String(timeRemaining.seconds).padStart(2, '0')}</Text><Text style={styles.timeLabel}>SECONDS</Text></View>
        </View>
        <Text style={styles.countdownInfoText}>Chapter {currentChapterData?.chap} is scheduled and will be available soon.</Text>
      </View>
    </Animated.View>
  );

  if (loading) {
    return (
      <View style={[styles.centerContainer, { backgroundColor: readerSettings.backgroundColor }]}>
        <StatusBar hidden /><ActivityIndicator size="large" color="#FF6B6B" />
        <Text style={styles.messageText}>Loading Chapter...</Text>
      </View>
    );
  }

  // --- RENDER ---
  return (
    <View style={[styles.container, { backgroundColor: readerSettings.backgroundColor }]}>
      <StatusBar hidden />
      
        {showCountdown ? (
          <Pressable style={styles.pressableArea} onPress={toggleControls}>
            {renderCountdown()}
          </Pressable>
        ) : (
          <FlatList
            ref={flatListRef}
            data={images}
            keyExtractor={(item, index) => `${item.uri.slice(0, 50)}-${index}`}
            renderItem={renderImage}
            getItemLayout={getItemLayout}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: insets.bottom + 50, backgroundColor: readerSettings.backgroundColor }}
            onScrollBeginDrag={() => { isScrolling.current = true; if (showControls) { clearTimeout(controlsTimeoutRef.current); setShowControls(false); }}}
            onTouchEnd={() => { if (!isScrolling.current) { toggleControls(); } isScrolling.current = false; }}
            onScroll={onScroll}
            scrollEventThrottle={16}
            decelerationRate={readerSettings.decelerationRate}
            maxToRenderPerBatch={5} initialNumToRender={3} windowSize={7}
            ListEmptyComponent={
                !showCountdown && !loading ? (
                    <View style={styles.centerContainer}>
                        <Ionicons name="image-outline" size={60} color="#555" />
                        <Text style={[styles.messageText, {marginTop: 15}]}>No pages found for this chapter.</Text>
                        <TouchableOpacity style={[styles.button, {marginTop: 20}]} onPress={handleRefresh}>
                          <Text style={styles.buttonText}>Tap to Refresh</Text>
                        </TouchableOpacity>
                    </View>
                ) : null
            }
            ListFooterComponent={
                images.length > 0 ? (
                  <View style={styles.footer}>
                    <Text style={styles.footerText}>End of Chapter {currentChapterData?.chap}</Text>
                    <TouchableOpacity
                      style={[styles.button, uniqueChapters.findIndex(ch => ch.hid === currentChapterData.hid) <= 0 && styles.disabledButton]}
                      onPress={() => handleNavigation('next')}
                      disabled={uniqueChapters.findIndex(ch => ch.hid === currentChapterData.hid) <= 0}>
                      <Text style={styles.buttonText}>Next Chapter</Text>
                    </TouchableOpacity>
                  </View>
                ) : null
            }
          />
        )}

      {showControls && ( <>
          {readerSettings.showProgressBar && !showCountdown && images.length > 0 && (
            <View style={[styles.progressBarContainer, { bottom: 60 + insets.bottom }]}>
                <View style={[styles.progressBar, { width: `${scrollProgress * 100}%` }]} />
            </View>
          )}
          <View style={[styles.header, { paddingTop: insets.top + 10 }]} onTouchStart={(e) => e.stopPropagation()}>
            <TouchableOpacity style={styles.iconButton} onPress={() => navigation.goBack()}>
              <Ionicons name="arrow-back" size={24} color="#FFF" />
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.chapterTitle} numberOfLines={1}>{`Ch. ${currentChapterData?.chap}`}</Text>
              <Text style={styles.scanlatorText}>{currentChapterData?.group_name?.[0] || 'Unknown'}</Text>
            </View>
            <View style={styles.headerRight}>
              <TouchableOpacity style={styles.iconButton} onPress={handleRefresh}>
                <Ionicons name="refresh" size={23} color="#FFF" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.iconButton} onPress={() => setShowSettingsModal(true)}>
                  <Ionicons name="ellipsis-vertical" size={22} color="#FFF" />
              </TouchableOpacity>
            </View>
          </View>
          {/* FIX: Comments button has been re-added here */}
          <View style={[styles.bottomNav, { paddingBottom: insets.bottom + 10 }]} onTouchStart={(e) => e.stopPropagation()}>
            <TouchableOpacity style={styles.navButton} onPress={() => handleNavigation('prev')} disabled={uniqueChapters.findIndex(ch => ch.hid === currentChapterData.hid) >= uniqueChapters.length-1}>
              <Ionicons name="chevron-back" size={28} color={uniqueChapters.findIndex(ch => ch.hid === currentChapterData.hid) >= uniqueChapters.length-1 ? "#555" : "#FFF"} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.navButton} onPress={() => setShowChapterModal(true)}>
              <Ionicons name="list" size={28} color="#FFF" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.navButton} onPress={handleOpenComments} disabled={showCountdown || loading}>
              <Ionicons name="chatbubble-ellipses-outline" size={26} color={showCountdown || loading ? "#555" : "#FFF"} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.navButton} onPress={() => setShowColorizerModal(true)} disabled={showCountdown || images.length === 0}>
              <Ionicons name="brush-outline" size={26} color={showCountdown || images.length === 0 ? "#555" : isChapterColorized ? '#FFD700' : '#FFF'} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.navButton} onPress={() => setShowScanlatorModal(true)} disabled={availableScanlators.length <= 1}>
               <Ionicons name="swap-vertical" size={28} color={availableScanlators.length > 1 ? "#FFF" : "#555"} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.navButton} onPress={() => handleNavigation('next')} disabled={uniqueChapters.findIndex(ch => ch.hid === currentChapterData.hid) <= 0}>
              <Ionicons name="chevron-forward" size={28} color={uniqueChapters.findIndex(ch => ch.hid === currentChapterData.hid) <= 0 ? "#555" : "#FFF"} />
            </TouchableOpacity>
          </View>
      </>)}
      
      <Modal visible={showColorizerModal} transparent animationType="fade" onRequestClose={() => setShowColorizerModal(false)}>
        <Pressable style={styles.modalBackdrop} onPress={() => setShowColorizerModal(false)}>
          <Pressable style={[styles.modalContent, { paddingBottom: 10 }]} onPress={()=>{}}>
              <Text style={styles.modalTitle}>Manga Colorizer</Text>
              
              <View style={styles.settingRow}>
                <Text style={styles.settingLabel}>Base URL</Text>
              </View>
              <TextInput 
                  style={styles.searchInput} 
                  placeholder="https://server.ngrok-free.app" 
                  placeholderTextColor="#888" 
                  value={colorizerSettings.baseUrl} 
                  onChangeText={(val) => updateColorizerSetting('baseUrl', val)}
                  autoCapitalize="none"
              />

              <View style={[styles.settingRow, {borderTopWidth: 1}]}>
                <Text style={styles.settingLabel}>Colorize</Text>
                <Switch trackColor={{false: '#767577', true: '#FF8A8A'}} thumbColor={colorizerSettings.colorize ? '#FF6B6B' : '#f4f3f4'} onValueChange={(val) => updateColorizerSetting('colorize', val)} value={colorizerSettings.colorize}/>
              </View>
              <View style={styles.settingRow}>
                <Text style={styles.settingLabel}>Upscale (x4)</Text>
                <Switch trackColor={{false: '#767577', true: '#FF8A8A'}} thumbColor={colorizerSettings.upscale ? '#FF6B6B' : '#f4f3f4'} onValueChange={(val) => updateColorizerSetting('upscale', val)} value={colorizerSettings.upscale}/>
              </View>
              <View style={styles.settingRow}>
                <Text style={styles.settingLabel}>Denoise</Text>
                <Switch trackColor={{false: '#767577', true: '#FF8A8A'}} thumbColor={colorizerSettings.denoise ? '#FF6B6B' : '#f4f3f4'} onValueChange={(val) => updateColorizerSetting('denoise', val)} value={colorizerSettings.denoise}/>
              </View>
             <View style={styles.settingRow}>
                <Text style={styles.settingLabel}>Use Backend Cache</Text>
                <Switch 
                    trackColor={{false: '#767577', true: '#FF8A8A'}} 
                    thumbColor={colorizerSettings.useBackendCache ? '#FF6B6B' : '#f4f3f4'} 
                    onValueChange={(val) => updateColorizerSetting('useBackendCache', val)} 
                    value={colorizerSettings.useBackendCache}
                />
              </View>

             {isChapterColorized && (
                <TouchableOpacity style={[styles.modalButton, {backgroundColor: '#555', marginTop: 15}]} onPress={handleRevertToOriginal}>
                    <Text style={styles.modalButtonText}>Revert to Original</Text>
                </TouchableOpacity>
             )}
             
              <TouchableOpacity style={[styles.modalButton, {backgroundColor: '#FF6B6B', marginTop: 10, marginBottom: 15}]} onPress={handleStartColorization}>
                  <Text style={styles.modalButtonText}>
                    {isChapterColorized ? 'Recolorize Chapter' : 'Start Colorization'}
                  </Text>
              </TouchableOpacity>
          </Pressable>
        </Pressable>
      </Modal>

            <Modal visible={showCommentsModal} transparent animationType="slide" onRequestClose={() => setShowCommentsModal(false)}>
        <View style={styles.commentsModalBackdrop}>
            <View style={[styles.commentsModalContent, {paddingBottom: insets.bottom}]}>
                <View style={styles.commentsHeader}>
                    {/* FIX: Title now includes the comment count */}
                  <Text style={styles.modalTitle}>
  Chapter Comments
                    {/* Show count only if there are comments, and use .length property */}
                    {comments.length > 0 && ` (${comments.length})`}
                    </Text>
                    <TouchableOpacity style={styles.closeButton} onPress={() => setShowCommentsModal(false)}>
                        <Ionicons name="close" size={24} color="#FFF"/>
                    </TouchableOpacity>
                </View>
                {commentsLoading ? (
                    <View style={styles.centerContainer}>
                        <ActivityIndicator size="large" color="#FF6B6B" />
                    </View>
                ) : comments.length > 0 ? (
                    <FlatList
                        data={comments}
                        renderItem={({item}) => <MemoizedComment comment={item} />}
                        keyExtractor={item => item.id.toString()}
                        contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 10 }}
                        ItemSeparatorComponent={() => <View style={styles.commentSeparator} />}
                    />
                ) : (
                    <View style={styles.centerContainer}>
                        <Text style={styles.messageText}>No comments found for this chapter.</Text>
                    </View>
                )}
            </View>
        </View>
       </Modal>
      
      <Modal visible={showChapterModal} transparent animationType="fade" onRequestClose={() => setShowChapterModal(false)}>
        <Pressable style={styles.modalBackdrop} onPress={() => setShowChapterModal(false)}>
          <Pressable style={[styles.modalContent, {paddingBottom: 20}]} onPress={()=>{}}>
            <Text style={styles.modalTitle}>Select Chapter</Text>
            <TextInput style={styles.searchInput} placeholder="Search by number or title..." placeholderTextColor="#888" value={searchQuery} onChangeText={setSearchQuery}/>
            <FlatList
              data={filteredChapters}
              keyExtractor={(item) => item.hid}
              renderItem={({ item }) => (
                <TouchableOpacity style={styles.modalItem} onPress={() => selectChapter(item)}>
                  <Text style={[styles.modalItemText, item.hid === currentChapterData?.hid && styles.activeItem]}>
                    {`Chapter ${item.chap}${item.title ? ` - ${item.title}` : ''}`}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </Pressable>
        </Pressable>
      </Modal>

      <Modal visible={showScanlatorModal} transparent animationType="fade" onRequestClose={() => setShowScanlatorModal(false)}>
        <Pressable style={styles.modalBackdrop} onPress={() => setShowScanlatorModal(false)}>
          <Pressable style={[styles.modalContent, {paddingBottom: 20}]} onPress={()=>{}}>
            <Text style={styles.modalTitle}>Select Scanlation Group</Text>
            <FlatList data={availableScanlators} keyExtractor={(item, index) => `${item.hid}-${index}`} renderItem={({ item }) => (
              <TouchableOpacity style={styles.modalItem} onPress={() => selectScanlator(item)}>
                <View style={styles.modalRow}>
                  <Text style={[styles.modalItemText, {flex: 1}, item.hid === currentChapterData?.hid && styles.activeItem]} numberOfLines={2}>
                    {item.group_name?.[0] || 'Unknown'}
                  </Text>
                  <Text style={[styles.modalItemSubtext, item.hid === currentChapterData?.hid && styles.activeItem]}>
                    {new Date(item.created_at).toLocaleDateString()}
                  </Text>
                </View>
              </TouchableOpacity>)}
            />
          </Pressable>
        </Pressable>
      </Modal>
      
      <Modal visible={showSettingsModal} transparent animationType="fade" onRequestClose={() => setShowSettingsModal(false)}>
        <Pressable style={styles.modalBackdrop} onPress={() => setShowSettingsModal(false)}>
          <Pressable style={[styles.modalContent, {paddingBottom: 10}]} onPress={()=>{}}>
              <Text style={styles.modalTitle}>Reader Settings</Text>
              <View style={styles.settingRow}>
                <Text style={styles.settingLabel}>Scrolling Feel</Text>
                <View style={styles.settingControl}>
                  <TouchableOpacity style={[styles.settingButton, readerSettings.decelerationRate === 0.998 && styles.settingButtonActive]} onPress={() => updateSetting('decelerationRate', 0.998)}>
                    <Text style={styles.settingButtonText}>Ultra</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.settingButton, readerSettings.decelerationRate === 'normal' && styles.settingButtonActive]} onPress={() => updateSetting('decelerationRate', 'normal')}>
                    <Text style={styles.settingButtonText}>Smooth</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.settingButton, readerSettings.decelerationRate === 'fast' && styles.settingButtonActive]} onPress={() => updateSetting('decelerationRate', 'fast')}>
                    <Text style={styles.settingButtonText}>Direct</Text>
                  </TouchableOpacity>
                </View>
              </View>
              <View style={styles.settingRow}>
                <Text style={styles.settingLabel}>Background</Text>
                <View style={styles.settingControl}>
                  <TouchableOpacity style={[styles.colorButton, {backgroundColor: '#000'}, readerSettings.backgroundColor === '#000' && styles.colorButtonActive]} onPress={() => updateSetting('backgroundColor', '#000')}/>
                  <TouchableOpacity style={[styles.colorButton, {backgroundColor: '#1C1C1E'}, readerSettings.backgroundColor === '#1C1C1E' && styles.colorButtonActive]} onPress={() => updateSetting('backgroundColor', '#1C1C1E')}/>
                </View>
              </View>
              <View style={[styles.settingRow, {borderBottomWidth: 0}]}>
                <Text style={styles.settingLabel}>Show Progress Bar</Text>
                <View style={styles.settingControl}>
                   <Switch trackColor={{false: '#767577', true: '#FF8A8A'}} thumbColor={readerSettings.showProgressBar ? '#FF6B6B' : '#f4f3f4'} onValueChange={(val) => updateSetting('showProgressBar', val)} value={readerSettings.showProgressBar}/>
                </View>
              </View>
          </Pressable>
        </Pressable>
      </Modal>

      <ProgressToast 
        isVisible={isColorizing}
        statusText={colorizationStatusText}
        progress={colorizationProgress}
        etaText={estimatedTimeRemaining}
      />
      <CustomToast message={toastConfig.message} isVisible={toastConfig.isVisible} onDismiss={dismissToast} />
    </View>
  );
}

const styles = StyleSheet.create({
    container: { flex: 1 },
    pressableArea: { flex: 1, justifyContent: 'center' },
    centerContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
    messageText: { color: '#CCC', fontSize: 16, textAlign: 'center' },
    button: { backgroundColor: '#FF6B6B', paddingVertical: 12, paddingHorizontal: 30, borderRadius: 25 },
    disabledButton: { backgroundColor: '#555' },
    buttonText: { color: '#FFF', fontSize: 16, fontWeight: 'bold' },
    chapterImage: { width: width },
    footer: { paddingVertical: 40, alignItems: 'center' },
    footerText: { color: '#AAA', fontSize: 16, marginBottom: 16 },
    header: { position: 'absolute', top: 0, left: 0, right: 0, backgroundColor: 'rgba(0, 0, 0, 0.75)', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 10, paddingBottom: 15, zIndex: 10, },
    headerCenter: { flex: 1, alignItems: 'center', paddingHorizontal: 10, },
    headerRight: { flexDirection: 'row', alignItems: 'center' },
    chapterTitle: { color: '#FFF', fontSize: 16, fontWeight: 'bold', textAlign: 'center' },
    scanlatorText: { color: '#DDD', fontSize: 12, opacity: 0.9 },
    iconButton: { padding: 8 },
    bottomNav: { position: 'absolute', bottom: 0, left: 0, right: 0, backgroundColor: 'rgba(0, 0, 0, 0.75)', flexDirection: 'row', justifyContent: 'space-around', alignItems: 'center', paddingVertical: 5, zIndex: 10, },
    navButton: { flex: 1, alignItems: 'center', padding: 10, },
    progressBarContainer: { position: 'absolute', left: 20, right: 20, height: 4, backgroundColor: 'rgba(255, 255, 255, 0.2)', zIndex: 20, borderRadius: 2, },
    progressBar: { height: '100%', backgroundColor: '#FF6B6B', borderRadius: 2, },
    modalBackdrop: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.85)' },
    modalContent: { backgroundColor: '#1E1E1E', borderRadius: 15, width: '90%', maxHeight: '80%', elevation: 10, overflow: 'hidden' },
    modalTitle: { color: '#FFF', fontSize: 20, fontWeight: 'bold', padding: 20, textAlign: 'center' },
    searchInput: { backgroundColor: '#333', color: '#FFF', borderRadius: 8, paddingHorizontal: 15, paddingVertical: 12, marginHorizontal: 20, marginBottom: 15, fontSize: 16 },
    modalItem: { paddingVertical: 16, paddingHorizontal: 20, borderTopWidth: 1, borderTopColor: '#333' },
    modalRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
    modalItemText: { color: '#EFEFEF', fontSize: 16 },
    modalItemSubtext: { color: '#AAA', fontSize: 12, marginLeft: 10, },
    activeItem: { color: '#FF6B6B', fontWeight: 'bold' },
    settingRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 16, paddingHorizontal: 20, borderTopWidth: 1, borderTopColor: '#333' },
    settingLabel: { color: '#EFEFEF', fontSize: 16 },
    settingControl: { flexDirection: 'row', alignItems: 'center' },
    settingButton: { paddingVertical: 7, paddingHorizontal: 16, backgroundColor: '#444', borderRadius: 20, marginLeft: 8 },
    settingButtonActive: { backgroundColor: '#FF6B6B' },
    settingButtonText: { color: '#FFF', fontWeight: 'bold', fontSize: 14 },
    colorButton: { width: 32, height: 32, borderRadius: 16, marginLeft: 10, borderWidth: 3, borderColor: 'transparent' },
    colorButtonActive: { borderColor: '#FFF' },
    toastBaseContainer: { position: 'absolute', left: 0, right: 0, alignItems: 'center', zIndex: 9999, },
    toastPill: { flexDirection: 'row', alignItems: 'center', backgroundColor: 'rgba(30, 30, 30, 0.95)', borderRadius: 50, paddingVertical: 10, paddingLeft: 15, paddingRight: 10, width: 'auto', maxWidth: '90%', minHeight: 50, borderColor: 'rgba(255,255,255,0.1)', borderWidth: 1, },
    toastMessage: { flexShrink: 1, color: '#FFF', fontSize: 15, fontWeight: '500' },
    toastCloseButton: { borderRadius: 15, width: 30, height: 30, justifyContent: 'center', alignItems: 'center', marginLeft: 5 },
    commentsModalBackdrop: { flex: 1, justifyContent: 'flex-end', backgroundColor: 'rgba(0, 0, 0, 0.5)' },
    commentsModalContent: { height: '85%', width: '100%', backgroundColor: '#181818', borderBottomLeftRadius: 0, borderBottomRightRadius: 0, borderTopLeftRadius: 20, borderTopRightRadius: 20, overflow: 'hidden'},
    commentsHeader: { flexDirection: 'row', justifyContent: 'center', alignItems: 'center', padding: 20, borderBottomWidth: 1, borderBottomColor: '#333'},
    closeButton: { position: 'absolute', right: 15, top: 18, padding: 5 },
    commentSeparator: { height: 1, backgroundColor: '#2a2a2a', marginVertical: 8 },
    commentContainer: { paddingVertical: 12 },
    replyContainer: { marginLeft: 20, marginTop: 10, paddingTop: 10, borderLeftWidth: 2, borderLeftColor: '#333', paddingLeft: 10 },
    deletedCommentText: { fontStyle: 'italic', color: '#888', fontSize: 14 },
    commentHeader: { flexDirection: 'row', alignItems: 'center', marginBottom: 8 },
    commentAvatar: { width: 36, height: 36, borderRadius: 18, backgroundColor: '#333' },
    commentHeaderInfo: { marginLeft: 10, flex: 1 },
    commentUsername: { color: '#EAEAEA', fontWeight: 'bold', fontSize: 15 },
    commentDate: { color: '#888', fontSize: 12 },
    commentContent: { color: '#DCDCDC', fontSize: 15, lineHeight: 22 },
      commentImage: { 
      width: '100%', 
      height: 400, // Increased height to allow tall images to display properly
      // REMOVED: aspectRatio: 16/9,
      borderRadius: 8, 
      marginVertical: 8, 
      backgroundColor: '#222'
    },
    commentFooter: { flexDirection: 'row', alignItems: 'center', marginTop: 10, opacity: 0.8 },
    commentVotes: { color: '#AAA', marginLeft: 4, fontSize: 14 },
    repliesSection: { marginTop: 10 },
    modalButton: { paddingVertical: 14, marginHorizontal: 20, borderRadius: 10, justifyContent: 'center', alignItems: 'center' },
    modalButtonText: { color: '#FFF', fontSize: 16, fontWeight: 'bold' },
    countdownContainer: { justifyContent: 'center', alignItems: 'center', paddingHorizontal: 20, },
    countdownContent: { backgroundColor: 'rgba(25, 25, 25, 0.85)', borderRadius: 20, padding: 25, alignItems: 'center', width: '100%', maxWidth: 380, borderWidth: 1, borderColor: 'rgba(255, 255, 255, 0.1)', shadowColor: '#000', shadowOffset: { width: 0, height: 10 }, shadowOpacity: 0.5, shadowRadius: 15, elevation: 20, },
    countdownHeader: { color: '#BBBBBB', fontSize: 14, fontWeight: '600', letterSpacing: 1.5, marginTop: 15, marginBottom: 20, },
    timerDisplay: { flexDirection: 'row', justifyContent: 'center', alignItems: 'center', },
    timeBlock: { alignItems: 'center', minWidth: 70, },
    timeNumber: { color: '#FFFFFF', fontSize: 42, fontWeight: '700', fontFamily: Platform.OS === 'ios' ? 'Courier New' : 'monospace', },
    timeLabel: { color: '#888888', fontSize: 11, fontWeight: 'bold', marginTop: 4, letterSpacing: 0.5, },
    timeSeparator: { color: '#555', fontSize: 30, fontWeight: '300', marginHorizontal: 5, paddingBottom: 20, },
    countdownInfoText: { color: '#AAAAAA', fontSize: 14, textAlign: 'center', marginTop: 25, lineHeight: 20, },
    
    progressToastContainer: {
        position: 'absolute',
        left: 0,
        right: 0,
        alignItems: 'center',
        zIndex: 9998,
    },
    progressToastPill: {
        width: '90%',
        backgroundColor: 'rgba(30, 30, 30, 0.95)',
        borderRadius: 25,
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderColor: 'rgba(255,255,255,0.1)',
        borderWidth: 1,
        justifyContent: 'center',
        overflow: 'hidden',
    },
    progressToastHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
    },
    progressStatusText: {
        color: '#EAEAEA',
        fontSize: 16,
        fontWeight: '500',
        marginLeft: 10,
        flex: 1,
    },
    progressBarBackground: {
        height: 8,
        width: '100%',
        backgroundColor: '#444',
        borderRadius: 4,
        marginTop: 10,
        overflow: 'hidden',
    },
    progressBarForeground: {
        height: '100%',
        backgroundColor: '#FF6B6B',
        borderRadius: 4,
    },
    progressEtaText: {
        color: '#AAA',
        fontSize: 13,
        marginTop: 8,
        alignSelf: 'center',
    },
});