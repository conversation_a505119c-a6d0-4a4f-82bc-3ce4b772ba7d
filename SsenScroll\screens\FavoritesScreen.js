import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { storageService } from '../services/storageService';
import { getCoverImageUrl, formatStatus } from '../services/api';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = (width - 60) / 2; // 2 columns with padding

// --- Reusable UI Components ---

const EmptyState = () => (
  <View style={styles.emptyContainer}>
    <Ionicons name="heart-outline" size={80} color="#666" />
    <Text style={styles.emptyTitle}>No Favorites Yet</Text>
    <Text style={styles.emptySubtitle}>
      Start adding manhwa to your favorites by tapping the heart icon on any manhwa detail page.
    </Text>
  </View>
);

const LoadingScreen = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#FFF" />
    <Text style={styles.loadingText}>Loading favorites...</Text>
  </View>
);

const FavoriteItem = ({ item, onPress, onRemove, progressData }) => (
  <TouchableOpacity style={styles.favoriteItem} onPress={() => onPress(item)}>
    <View style={styles.imageContainer}>
      <Image
        source={{ uri: getCoverImageUrl(item.cover) }}
        style={styles.coverImage}
        placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
        contentFit="cover"
        transition={200}
      />
      {progressData && progressData.progressPercentage > 0 && (
        <View style={styles.progressOverlay}>
          <View style={[styles.progressBar, { width: `${progressData.progressPercentage}%` }]} />
        </View>
      )}
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => onRemove(item)}
      >
        <Ionicons name="close" size={20} color="#FF6B6B" />
      </TouchableOpacity>
    </View>
    <View style={styles.itemInfo}>
      <Text style={styles.itemTitle} numberOfLines={2}>{item.title}</Text>
      <View style={styles.itemStats}>
        <View style={styles.statRow}>
          <Ionicons name="flag" size={12} color="#FFC107" />
          <Text style={styles.statText}>{formatStatus(item.status)}</Text>
        </View>
        {item.rating && (
          <View style={styles.statRow}>
            <Ionicons name="star" size={12} color="#FFC107" />
            <Text style={styles.statText}>{parseFloat(item.rating).toFixed(1)}</Text>
          </View>
        )}
        {progressData && progressData.progressPercentage > 0 && (
          <View style={styles.statRow}>
            <Ionicons name="book" size={12} color="#4CAF50" />
            <Text style={[styles.statText, { color: '#4CAF50' }]}>
              {progressData.progressPercentage}% read
            </Text>
          </View>
        )}
      </View>
    </View>
  </TouchableOpacity>
);

// --- Main FavoritesScreen Component ---
export default function FavoritesScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const [favorites, setFavorites] = useState([]);
  const [progressData, setProgressData] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadFavorites = async () => {
    try {
      const [favoritesData, allProgressData] = await Promise.all([
        storageService.getFavorites(),
        storageService.getAllReadingProgress()
      ]);
      setFavorites(favoritesData);
      setProgressData(allProgressData);
    } catch (error) {
      console.error('Error loading favorites:', error);
      Alert.alert('Error', 'Failed to load favorites. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Load favorites when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadFavorites();
    }, [])
  );

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    loadFavorites();
  }, []);

  const navigateToDetail = (favorite) => {
    navigation.navigate('ManhwaDetail', {
      slug: favorite.slug,
      hid: favorite.hid,
      title: favorite.title,
    });
  };

  const handleRemoveFavorite = (favorite) => {
    Alert.alert(
      'Remove from Favorites',
      `Are you sure you want to remove "${favorite.title}" from your favorites?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await storageService.removeFromFavorites(favorite.slug);
              // Update local state immediately for better UX
              setFavorites(prev => prev.filter(fav => fav.slug !== favorite.slug));
            } catch (error) {
              console.error('Error removing favorite:', error);
              Alert.alert('Error', 'Failed to remove from favorites. Please try again.');
            }
          },
        },
      ]
    );
  };

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top + 6 }]}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Favorites</Text>
        <View style={styles.headerRight}>
          <Text style={styles.favoriteCount}>{favorites.length}</Text>
        </View>
      </View>

      {/* Content */}
      {favorites.length === 0 ? (
        <EmptyState />
      ) : (
        <FlatList
          data={favorites}
          keyExtractor={(item) => item.slug}
          numColumns={2}
          contentContainerStyle={styles.listContainer}
          columnWrapperStyle={styles.row}
          showsVerticalScrollIndicator={false}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          initialNumToRender={8}
          maxToRenderPerBatch={6}
          updateCellsBatchingPeriod={50}
          windowSize={11}
          removeClippedSubviews
          renderItem={({ item }) => (
            <FavoriteItem
              item={item}
              onPress={navigateToDetail}
              onRemove={handleRemoveFavorite}
              progressData={progressData[item.slug]}
            />
          )}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0F0F12',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.05)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(30,30,36,0.6)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.05)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerRight: {
    width: 40,
    alignItems: 'center',
  },
  favoriteCount: {
    color: '#FFC107',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0F0F12',
  },
  loadingText: {
    color: '#FFF',
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    color: '#FFF',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    color: '#AAA',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  listContainer: {
    padding: 20,
  },
  row: {
    justifyContent: 'space-between',
  },
  favoriteItem: {
    width: ITEM_WIDTH,
    marginBottom: 20,
    backgroundColor: 'rgba(30,30,36,0.6)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
  },
  coverImage: {
    width: '100%',
    height: ITEM_WIDTH * 1.4,
  },
  progressOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemInfo: {
    padding: 12,
  },
  itemTitle: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  itemStats: {
    gap: 4,
  },
  statRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    color: '#AAA',
    fontSize: 12,
  },
});
