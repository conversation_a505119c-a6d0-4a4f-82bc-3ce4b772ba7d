import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  Dimensions,
  ActivityIndicator,
  StatusBar,
  Linking,
  Image as RNImage,
  Pressable,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  useAnimatedScrollHandler,
  interpolate,
  Extrapolate,
  runOnJS
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { comickAPI, getCoverImageUrl, formatStatus } from '../services/api';
import { storageService } from '../services/storageService';
import { LinearGradient } from 'expo-linear-gradient';
import { detectComicProvider } from '../utils/provider';

const { width, height } = Dimensions.get('window');
const TABS = ['Chapters', 'Details', 'Similar'];
const EST_ROW_HEIGHT = 72; // approximate collapsed chapter row height for getItemLayout perf

const AnimatedFlatList = Animated.createAnimatedComponent(FlatList);

// small helpers
const formatFollowCount = (count) => {
  if (!count) return '0';
  if (count < 1000) return count.toString();
  return `${(count / 1000).toFixed(1).replace('.0', '')}k`;
};

const Loading = () => (
  <View style={localStyles.loader}>
    <ActivityIndicator size="large" color="#A9F1B6" />
  </View>
);

const TopNav = ({ onBack, insets, isFavorited, onToggleFavorite, toggling }) => (
  <View style={[localStyles.topNav, { paddingTop: insets.top + 6 }]}>
    <TouchableOpacity style={localStyles.topIcon} onPress={onBack}>
      <Ionicons name="arrow-back" size={22} color="#FFF" />
    </TouchableOpacity>
    <View style={{ flexDirection: 'row', gap: 8 }}>
      <TouchableOpacity style={[localStyles.topIcon]} onPress={onToggleFavorite} disabled={toggling}>
        <Ionicons name={isFavorited ? 'heart' : 'heart-outline'} size={20} color={isFavorited ? '#FF7A7A' : '#FFF'} />
      </TouchableOpacity>
      <TouchableOpacity style={localStyles.topIcon}>
        <Ionicons name="share-social-outline" size={20} color="#FFF" />
      </TouchableOpacity>
    </View>
  </View>
);

const ComicHeader = ({ comic, author, onContinue, progress, animatedStyles }) => {
  const coverUri = getCoverImageUrl(comic?.md_covers?.[0] || comic?.md_covers?.[1]);
  return (
    <Animated.View style={[localStyles.headerWrap, animatedStyles.container]}>
      <Animated.View style={[localStyles.headerBackdrop, animatedStyles.backdrop]} />
      <Animated.View style={[localStyles.coverWrap, animatedStyles.coverWrap]}>
        <Image
          source={{ uri: coverUri }}
          style={localStyles.coverImage}
          contentFit="cover"
          placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
        />
      </Animated.View>

      <Animated.View style={[localStyles.headerInfo, animatedStyles.info]}>
        <Text style={localStyles.title} numberOfLines={2}>{comic?.title}</Text>
        <Text style={localStyles.author}>By {author || 'Unknown'}</Text>

        <View style={localStyles.badgeRow}>
          {comic?.follow_rank ? (
            <View style={[localStyles.badge, { borderColor: 'rgba(122, 223, 255, 0.15)', backgroundColor: 'rgba(122,223,255,0.06)' }]}>
              <Ionicons name="podium-outline" size={14} color="#7ADFFF" />
              <Text style={[localStyles.badgeText, { color: '#7ADFFF' }]}>#{comic.follow_rank}</Text>
            </View>
          ) : null}
          <View style={[localStyles.badge, { borderColor: 'rgba(255, 222, 106, 0.12)', backgroundColor: 'rgba(255,222,106,0.04)' }]}>
            <Ionicons name="flag-outline" size={13} color="#FFDE6A" />
            <Text style={[localStyles.badgeText, { color: '#FFDE6A' }]}>{formatStatus(comic?.status)}</Text>
          </View>
          <View style={[localStyles.badge, { borderColor: 'rgba(255, 107, 107, 0.12)', backgroundColor: 'rgba(255,107,107,0.04)' }]}>
            <Ionicons name="people-outline" size={13} color="#FF9E9E" />
            <Text style={[localStyles.badgeText, { color: '#FF9E9E' }]}>{formatFollowCount(comic?.user_follow_count)}</Text>
          </View>
        </View>

        {progress?.progressPercentage > 0 && (
          <View style={localStyles.progressRow}>
            <View style={localStyles.progressBar}>
              <View style={[localStyles.progressFill, { width: `${progress.progressPercentage}%` }]} />
            </View>
            <Text style={localStyles.progressText}>{progress.progressPercentage}% complete</Text>
          </View>
        )}

        <TouchableOpacity onPress={onContinue} activeOpacity={0.9}>
          <LinearGradient
            colors={['#6B8CFF', '#5E5CE6']}
            start={[0, 0]}
            end={[1, 1]}
            style={localStyles.cta}
          >
            <Text style={localStyles.ctaText}>
              {progress?.lastChapterNumber ? `Continue Ch. ${progress.lastChapterNumber}` : 'Start Reading'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};

const TabBar = ({ active, onPress, chapterCount, animValues }) => {
  const indicatorStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: animValues.indicatorX.value }],
    };
  });

  return (
    <View style={localStyles.tabWrap}>
      <View style={localStyles.tabRow}>
        {TABS.map((t, idx) => (
          <TouchableOpacity key={t} onPress={() => onPress(t)} style={localStyles.tabButton}>
            <Text style={[localStyles.tabText, active === t && localStyles.tabTextActive]}>
              {t} {t === 'Chapters' ? `(${chapterCount})` : ''}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      <Animated.View style={[localStyles.tabIndicator, indicatorStyle]} />
    </View>
  );
};

const DetailsContent = ({ comic }) => {
  const provider = useMemo(() => detectComicProvider(comic), [comic]);
  const handleOpen = useCallback(async () => {
    if (!provider?.url) return;
    const supported = await Linking.canOpenURL(provider.url);
    if (supported) await Linking.openURL(provider.url);
    else Alert.alert('Error', `Can't open this URL: ${provider.url}`);
  }, [provider]);

  return (
    <View style={localStyles.content}>
      <Text style={localStyles.sectionTitle}>Synopsis</Text>
      <Text style={localStyles.description}>{comic?.desc || 'No description available.'}</Text>

      {provider?.url && (
        <>
          <Text style={localStyles.sectionTitle}>Read Officially</Text>
          <TouchableOpacity style={localStyles.rawButton} onPress={handleOpen}>
            <Ionicons name={provider.icon?.name || 'globe-outline'} size={20} color={provider.icon?.color || '#A1A1AA'} />
            <Text style={localStyles.rawText}>Open on {provider.name}</Text>
          </TouchableOpacity>
        </>
      )}

      <Text style={localStyles.sectionTitle}>Genres</Text>
      <View style={localStyles.genres}>
        {comic?.md_comic_md_genres?.map((g, i) => (
          <View key={i} style={localStyles.genreChip}>
            <Text style={localStyles.genreText}>{g.md_genres.name}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const SimilarContent = ({ recommendations, onNavigate }) => {
  const similar = (recommendations || []).map(r => r.relates).filter(Boolean);
  if (!similar.length) return <View style={localStyles.content}><Text style={localStyles.description}>No similar comics found.</Text></View>;
  return (
    <View style={localStyles.content}>
      <Text style={localStyles.sectionTitle}>Similar</Text>
      <FlatList
        data={similar}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(it) => it.hid}
        renderItem={({ item }) => (
          <TouchableOpacity style={localStyles.similarCard} onPress={() => onNavigate(item)}>
            <Image source={{ uri: getCoverImageUrl(item?.md_covers?.[0]) }} style={localStyles.similarImage} contentFit="cover" />
            <Text numberOfLines={2} style={localStyles.similarTitle}>{item.title}</Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );
};

// --- FIX: Wrapped in React.memo for performance ---
const ChapterRow = React.memo(({ group, onOpen, readingProgress, isExpanded, onToggleExpand, disableAnimations = false }) => {
  const hasMultipleScans = group.chapters.length > 1;
  const isRead = readingProgress?.lastChapterNumber && (parseFloat(group.chapterNumber) <= parseFloat(readingProgress.lastChapterNumber));

  const expandAnim = useSharedValue(0);
  const contentHeight = useSharedValue(0);
  const mountAnim = useSharedValue(disableAnimations ? 1 : 0);

  useEffect(() => {
    if (!disableAnimations) {
      mountAnim.value = withTiming(1, { duration: 300 });
    }
  }, [disableAnimations]);

  useEffect(() => {
    expandAnim.value = withTiming(isExpanded ? 1 : 0, { duration: 250 });
  }, [isExpanded]);

  const handleShortPress = () => {
    onOpen(group.defaultChapter);
  };
  
  // --- FIX: Restored long press functionality ---
  const handleLongPress = () => {
    if (hasMultipleScans) {
      // The passed 'onToggleExpand' prop now expects the chapter number
      onToggleExpand(group.chapterNumber);
    }
  };

  const mountStyle = useAnimatedStyle(() => ({
    opacity: mountAnim.value,
    transform: [{ translateY: interpolate(mountAnim.value, [0, 1], [15, 0]) }],
  }));

  const iconContainerStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: hasMultipleScans ? withTiming(isExpanded ? '90deg' : '0deg') : '0deg'}],
  }));
  
  const expandStyle = useAnimatedStyle(() => {
    const height = interpolate(expandAnim.value, [0, 1], [0, contentHeight.value]);
    return { height, opacity: expandAnim.value };
  });

  return (
    <Animated.View style={[localStyles.chapterCard, mountStyle, isRead && localStyles.cardRead]}>
      <Pressable
        onPress={handleShortPress}
        onLongPress={handleLongPress}
        style={localStyles.chapterInner}
        android_ripple={{ color: 'rgba(255,255,255,0.08)' }}
      >
        <View style={{ flex: 1, marginRight: 8 }}>
          <Text style={[localStyles.chapterTitle, isRead && localStyles.chapterTitleRead]}>Chapter {group.chapterNumber}</Text>
          <Text style={[localStyles.chapterDate, isRead && localStyles.chapterDateRead]}>
            {new Date(group.defaultChapter.created_at).toLocaleDateString()}
            {hasMultipleScans && ` • ${group.chapters.length} groups`}
          </Text>
        </View>

        {isRead ? (
           <Ionicons name="checkmark-circle" size={20} color="#6CE08A" />
        ) : (
          <Animated.View style={iconContainerStyle}>
             <Ionicons name={hasMultipleScans ? "ellipsis-horizontal" : "chevron-forward"} size={20} color="#C9C8D6" />
          </Animated.View>
        )}
      </Pressable>
      
      {hasMultipleScans && (
        <Animated.View style={[localStyles.scanContainer, expandStyle]}>
          <View onLayout={(e) => { contentHeight.value = e.nativeEvent.layout.height; }} style={localStyles.scanInnerContainer}>
             {group.chapters.map(chapter => (
                <TouchableOpacity key={chapter.hid} style={localStyles.scanRow} onPress={() => onOpen(chapter)} activeOpacity={0.7}>
                    <Ionicons name="people-circle-outline" size={16} color="#A8A6B8" />
                    <Text style={localStyles.scanText} numberOfLines={1}>
                        {chapter.md_chapters_groups?.[0]?.md_groups?.title || chapter.group_name?.[0] || 'Unknown Group'}
                    </Text>
                    <Text style={localStyles.scanDate}>{chapter._dateLabel}</Text>
                </TouchableOpacity>
             ))}
          </View>
        </Animated.View>
      )}
    </Animated.View>
  );
});


export default function ManhwaDetailScreen({ route, navigation }) {
  const { slug, hid } = route.params || {};
  const insets = useSafeAreaInsets();

  const [comicDetails, setComicDetails] = useState(null);
  const [groupedChapters, setGroupedChapters] = useState([]);
  const [allChaptersRaw, setAllChaptersRaw] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('Chapters');
  const [isFavorited, setIsFavorited] = useState(false);
  const [toggling, setToggling] = useState(false);
  const [readingProgress, setReadingProgress] = useState(null);
  const [toast, setToast] = useState({ visible: false, message: '' });
  const [expandedChapter, setExpandedChapter] = useState(null);

  const scrollY = useSharedValue(0);
  const indicatorX = useSharedValue(0);
  const toastTranslateY = useSharedValue(-120);
  const toastOpacity = useSharedValue(0);
  const toastTimerRef = useRef(null);

  const animValues = useMemo(() => ({ indicatorX, scrollY }), []);
  
  const toastAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: toastTranslateY.value }],
    opacity: toastOpacity.value,
  }));

  const headerAnimated = {
    container: useAnimatedStyle(() => ({
      transform: [{ translateY: interpolate(scrollY.value, [0, 160], [0, -30], Extrapolate.CLAMP) }],
      zIndex: 5
    })),
    backdrop: useAnimatedStyle(() => ({
      opacity: interpolate(scrollY.value, [0, 160], [0.9, 0.9], Extrapolate.CLAMP)
    })),
    coverWrap: useAnimatedStyle(() => ({
      transform: [{ scale: interpolate(scrollY.value, [0, 160], [1, 0.86], Extrapolate.CLAMP) }],
    })),
    info: useAnimatedStyle(() => ({
      opacity: interpolate(scrollY.value, [0, 120], [1, 0.85], Extrapolate.CLAMP),
      transform: [{ translateY: interpolate(scrollY.value, [0, 160], [0, -6], Extrapolate.CLAMP) }]
    }))
  };

  const onScroll = useAnimatedScrollHandler({
    onScroll: (ev) => {
      scrollY.value = ev.contentOffset.y;
    }
  });

  const sortedChapters = useMemo(() => {
    if (activeTab !== 'Chapters') return [];
    return [...groupedChapters].sort((a, b) => parseFloat(b.chapterNumber) - parseFloat(a.chapterNumber));
  }, [groupedChapters, activeTab]);

  useEffect(() => {
    let mounted = true;
    const load = async () => {
      if (!slug) return;
      setLoading(true);
      try {
        const comicData = await comickAPI.getComicDetails(slug);
        if (!mounted) return;
        setComicDetails(comicData);
        
        const comicHid = comicData?.comic?.hid || hid;
        if (comicHid) {
          const chaptersData = await comickAPI.getComicChapters(comicHid, { limit: 10000, lang: 'en' });
          if (!mounted) return;
          const englishChapters = chaptersData.chapters || [];
          setAllChaptersRaw(englishChapters);

          const groups = new Map();
          englishChapters.forEach(ch => {
            // Precompute date label for perf (avoid new Date per render)
            ch._dateLabel = new Date(ch.created_at).toLocaleDateString();
            const n = ch.chap || 'Unknown';
            if (!groups.has(n)) groups.set(n, []);
            groups.get(n).push(ch);
          });
          const grouped = Array.from(groups.entries()).map(([num, arr]) => {
            const sortedByGroup = arr.sort((a, b) => {
              const nameA = a.md_chapters_groups?.[0]?.md_groups?.title || a.group_name?.[0] || '';
              const nameB = b.md_chapters_groups?.[0]?.md_groups?.title || b.group_name?.[0] || '';
              return nameA.localeCompare(nameB);
            });
            const latest = [...arr].sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0];
            return {
              chapterNumber: num,
              chapters: sortedByGroup,
              defaultChapter: latest,
              defaultChapterDateLabel: latest?._dateLabel,
            };
          });
          setGroupedChapters(grouped);
        }
      } catch (e) {
        console.error("API Error in ManhwaDetailScreen:", e);
        Alert.alert('Error', "Failed to load comic content. Please try again later.");
      } finally {
        if (mounted) setLoading(false);
      }
    };
    load();
    return () => { mounted = false; };
  }, [slug, hid]);

  useFocusEffect(useCallback(() => {
    let mounted = true;
    (async () => {
      if (!slug) return;
      try {
        const [fav, progress] = await Promise.all([
          storageService.isFavorited(slug),
          storageService.getReadingProgress(slug)
        ]);
        if (!mounted) return;
        setIsFavorited(fav);
        setReadingProgress(progress);
      } catch (e) {
        console.log("Error loading user data:", e);
      }
    })();
    return () => { mounted = false; };
  }, [slug]));
    
  useEffect(() => {
    if (toastTimerRef.current) clearTimeout(toastTimerRef.current);
    if (toast.visible) {
      toastTranslateY.value = withSpring(insets.top, { damping: 20, stiffness: 150 });
      toastOpacity.value = withTiming(1, { duration: 300 });

      toastTimerRef.current = setTimeout(() => {
        toastTranslateY.value = withTiming(-120, { duration: 350 });
        toastOpacity.value = withTiming(0, { duration: 250 });
        setTimeout(() => setToast(t => ({ ...t, visible: false })), 350);
      }, 3500);
    } else {
      toastTranslateY.value = withTiming(-120, { duration: 350 });
      toastOpacity.value = withTiming(0, { duration: 250 });
    }
    return () => { if (toastTimerRef.current) clearTimeout(toastTimerRef.current) };
  }, [toast]);

  const showToast = useCallback((msg) => {
    setToast({ visible: true, message: msg });
  }, []);

  const handleToggleFavorite = useCallback(async () => {
    if (toggling || !comicDetails?.comic) return;
    const prev = isFavorited;
    setIsFavorited(s => !s);
    setToggling(true);
    try {
      const payload = { slug: comicDetails.comic.slug, hid: comicDetails.comic.hid, title: comicDetails.comic.title, md_covers: comicDetails.comic.md_covers };
      const newStatus = await storageService.toggleFavorite(payload);
      showToast(newStatus ? 'Added to favorites' : 'Removed from favorites');
    } catch (e) {
      setIsFavorited(prev);
      Alert.alert('Error', 'Could not update favorites.');
    } finally {
      setToggling(false);
    }
  }, [toggling, comicDetails, isFavorited, showToast]);
  
  // --- FIX: The handler function is memoized and now receives the chapter number ---
  const handleToggleChapterExpand = useCallback((chapterNumber) => {
    setExpandedChapter(current => (current === chapterNumber ? null : chapterNumber));
  }, []);

  const handleTabPress = useCallback((tab) => {
    setActiveTab(tab);
    const idx = TABS.indexOf(tab);
    indicatorX.value = withTiming(idx * (width / TABS.length), { duration: 300 });
  }, []);

  const navigateToChapterReader = useCallback((chapter, allList, manhwaInfo) => {
    if (!chapter) { Alert.alert('Error', 'Chapter data not available.'); return; }
    if (!allList || allList.length === 0) { Alert.alert('Error', 'Chapter list is empty.'); return; }
    navigation.navigate('ChapterReader', { currentChapter: chapter, allChapters: allList, manhwaInfo });
  }, [navigation]);

  // --- FIX: This new memoized handler is passed to ChapterRow ---
  const handleOpenChapter = useCallback((chapter) => {
    const manhwaInfo = {
        slug: comicDetails?.comic?.slug,
        hid: comicDetails?.comic?.id,
        title: comicDetails?.comic?.title,
        cover: comicDetails?.comic?.md_covers?.[0],
    };
    navigateToChapterReader(chapter, allChaptersRaw, manhwaInfo);
  }, [navigateToChapterReader, allChaptersRaw, comicDetails]);

  const handleContinue = useCallback(() => {
    if (!allChaptersRaw || allChaptersRaw.length === 0) { Alert.alert('No chapters', 'Chapters are still loading.'); return; }
    const first = [...allChaptersRaw].sort((a, b) => parseFloat(a.chap) - parseFloat(b.chap))[0];
    let chapterToOpen = first;
    
    if (readingProgress?.lastChapterHid) {
      const lastReadChapter = allChaptersRaw.find(c => c.hid === readingProgress.lastChapterHid);
      if (lastReadChapter) chapterToOpen = lastReadChapter;
    }
    
    if (chapterToOpen) {
      handleOpenChapter(chapterToOpen);
    } else {
      Alert.alert('No chapters found', 'Could not find a chapter to continue from.');
    }
  }, [allChaptersRaw, readingProgress, handleOpenChapter]);

  // --- FIX: renderChapter is now properly memoized, passing stable functions to ChapterRow ---
  const disableRowAnimations = groupedChapters.length > 250;

  const renderChapter = useCallback(({ item }) => (
    <ChapterRow
        group={item}
        onOpen={handleOpenChapter}
        readingProgress={readingProgress}
        isExpanded={item.chapterNumber === expandedChapter}
        onToggleExpand={handleToggleChapterExpand}
        disableAnimations={disableRowAnimations}
    />
  ), [readingProgress, expandedChapter, handleOpenChapter, handleToggleChapterExpand, disableRowAnimations]);


  if (loading || !comicDetails) return <Loading />;

  return (
    <View style={localStyles.screen}>
      <StatusBar barStyle="light-content" />
      <TopNav onBack={() => navigation.goBack()} insets={insets} isFavorited={isFavorited} onToggleFavorite={handleToggleFavorite} toggling={toggling} />
      
      <Animated.View style={[localStyles.toast, toastAnimatedStyle]}>
        <View style={localStyles.toastInner}>
            <Text style={localStyles.toastText}>{toast.message}</Text>
            <TouchableOpacity onPress={() => setToast(t => ({ ...t, visible: false }))}>
                <Ionicons name="close" size={18} color="#FFF" />
            </TouchableOpacity>
        </View>
      </Animated.View>

      <AnimatedFlatList
        data={activeTab === 'Chapters' ? sortedChapters : []}
        keyExtractor={(item) => item.chapterNumber}
        renderItem={renderChapter}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 80 }}
        onScroll={onScroll}
        scrollEventThrottle={16}
        extraData={expandedChapter}
        initialNumToRender={12}
        maxToRenderPerBatch={8}
        updateCellsBatchingPeriod={50}
        windowSize={11}
        removeClippedSubviews
        getItemLayout={expandedChapter === null ? ((_, index) => ({ length: EST_ROW_HEIGHT, offset: EST_ROW_HEIGHT * index, index })) : undefined}
        ListHeaderComponent={
          <>
            <ComicHeader
              comic={comicDetails.comic}
              author={comicDetails.authors?.[0]?.name}
              onContinue={handleContinue}
              progress={readingProgress}
              animatedStyles={headerAnimated}
            />
            <TabBar active={activeTab} onPress={handleTabPress} chapterCount={groupedChapters.length} animValues={animValues} />
          </>
        }
        ListFooterComponent={
          <>
            {activeTab === 'Details' && <DetailsContent comic={comicDetails.comic} />}
            {activeTab === 'Similar' && <SimilarContent recommendations={comicDetails.comic?.recommendations || []} onNavigate={(c) => navigation.push('ManhwaDetail', { slug: c.slug, hid: c.hid })} />}
          </>
        }
      />
    </View>
  );
}

const localStyles = StyleSheet.create({
  screen: { flex: 1, backgroundColor: '#0F0F12' },
  loader: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0F0F12' },
  topNav: { position: 'absolute', top: 0, left: 12, right: 12, zIndex: 40, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  topIcon: { width: 44, height: 44, borderRadius: 12, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(255,255,255,0.03)' },
  headerWrap: { paddingTop: 64, paddingHorizontal: 18, paddingBottom: 20, alignItems: 'center', marginBottom: 8 },
  headerBackdrop: { position: 'absolute', top: 0, left: 0, right: 0, height: 320, backgroundColor: '#121217', borderBottomLeftRadius: 20, borderBottomRightRadius: 20, zIndex: -1 },
  coverWrap: { marginTop: 10, shadowColor: '#000', shadowOpacity: 0.3, shadowRadius: 12, elevation: 12 },
  coverImage: { width: width * 0.33, height: width * 0.48, borderRadius: 12 },
  headerInfo: { marginTop: 14, width: width - 40, alignItems: 'center' },
  title: { color: '#FFFFFF', fontSize: 20, fontWeight: '700', textAlign: 'center' },
  author: { color: '#BDB8E8', fontSize: 13, marginTop: 6 },
  badgeRow: { flexDirection: 'row', marginTop: 10, gap: 8, flexWrap: 'wrap', justifyContent: 'center' },
  badge: { flexDirection: 'row', alignItems: 'center', borderWidth: 1, borderRadius: 14, paddingHorizontal: 8, paddingVertical: 6 },
  badgeText: { marginLeft: 6, fontSize: 13, fontWeight: '600' },
  progressRow: { width: '100%', marginTop: 10, alignItems: 'center' },
  progressBar: { width: '78%', height: 6, backgroundColor: 'rgba(255,255,255,0.06)', borderRadius: 4, overflow: 'hidden' },
  progressFill: { height: '100%', backgroundColor: '#6CE08A' },
  progressText: { marginTop: 6, color: '#9EE5B4', fontSize: 12 },
  cta: { marginTop: 12, borderRadius: 30, paddingVertical: 12, paddingHorizontal: 24, minWidth: 220, alignItems: 'center' },
  ctaText: { color: '#FFF', fontSize: 15, fontWeight: '700' },
  tabWrap: { marginTop: 12, paddingHorizontal: 12 },
  tabRow: { flexDirection: 'row', backgroundColor: 'transparent', borderBottomWidth: 1, borderBottomColor: 'rgba(255,255,255,0.03)' },
  tabButton: { flex: 1, paddingVertical: 14, alignItems: 'center' },
  tabText: { color: '#A8A6B8', fontSize: 17, fontWeight: '600' },
  tabTextActive: { color: '#FFF', fontSize: 17, fontWeight: '600' },
  tabIndicator: { height: 3, width: width / TABS.length, backgroundColor: '#6B8CFF', position: 'absolute', bottom: 0, left: 0, borderRadius: 2 },
  content: { paddingHorizontal: 18, paddingVertical: 18, paddingBottom: 36 },
  sectionTitle: { color: '#FFF', fontSize: 18, fontWeight: '700', marginBottom: 8 },
  description: { color: '#CFCFE0', fontSize: 14, lineHeight: 20 },
  rawButton: { flexDirection: 'row', alignItems: 'center', marginTop: 8, padding: 10, backgroundColor: 'rgba(255,255,255,0.02)', borderRadius: 12, alignSelf: 'flex-start' },
  rawLogo: { width: 36, height: 36, borderRadius: 6, marginRight: 10 },
  rawText: { color: '#EDEFF7', fontWeight: '700' },
  genres: { flexDirection: 'row', flexWrap: 'wrap', marginTop: 8, gap: 8 },
  genreChip: { paddingHorizontal: 12, paddingVertical: 8, borderRadius: 14, backgroundColor: 'rgba(255,255,255,0.02)', marginRight: 8, marginBottom: 8 },
  genreText: { color: '#C9C7DB', fontWeight: '600' },
  chapterCard: { marginHorizontal: 16, marginVertical: 6, borderRadius: 12, backgroundColor: 'rgba(30,30,36,0.6)', borderWidth: 1, borderColor: 'rgba(255,255,255,0.04)', overflow: 'hidden' },
  cardRead: { backgroundColor: 'rgba(24,24,28,0.5)', opacity: 0.8 },
  chapterInner: { flexDirection: 'row', paddingVertical: 14, paddingHorizontal: 16, justifyContent: 'space-between', alignItems: 'center' },
  chapterTitle: { color: '#EEEFF5', fontSize: 15, fontWeight: '600', marginBottom: 2 },
  chapterTitleRead: { color: '#A9A5B8' },
  chapterDate: { color: '#9B98A9', fontSize: 12 },
  chapterDateRead: { color: '#83808F' },
  scanContainer: { overflow: 'hidden', backgroundColor: 'rgba(0,0,0,0.15)', },
  scanInnerContainer: { paddingTop: 4, paddingBottom: 10, borderTopWidth: 1, borderTopColor: 'rgba(255,255,255,0.03)' },
  scanRow: { flexDirection: 'row', alignItems: 'center', paddingVertical: 8, paddingHorizontal: 16 },
  scanText: { color: '#C2C0D1', fontWeight: '500', marginLeft: 8, flex: 1 },
  scanDate: { color: '#9B98A9', fontSize: 12 },
  similarCard: { width: width * 0.36, marginRight: 12 },
  similarImage: { width: '100%', height: width * 0.5, borderRadius: 10 },
  similarTitle: { color: '#FFF', marginTop: 8, fontWeight: '700' },
  toast: { position: 'absolute', left: 16, right: 16, zIndex: 999, top: 0 },
  toastInner: { backgroundColor: 'rgba(20,20,22,0.95)', padding: 16, borderRadius: 14, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', shadowColor: "#000", shadowOffset: { width: 0, height: 4 }, shadowOpacity: 0.1, shadowRadius: 12, elevation: 8 },
  toastText: { color: '#FFF', fontWeight: '600', flex: 1, marginRight: 8 },
});