import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { comickAPI, getCoverImageUrl } from '../services/api';
import { storageService } from '../services/storageService';

const NotificationItem = ({ item, onPress }) => (
  <TouchableOpacity style={styles.notificationCard} onPress={() => onPress(item)}>
    <Image
      source={{ uri: getCoverImageUrl(item.manhwaCover) }}
      style={styles.notificationImage}
      placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
    />
    <View style={styles.notificationContent}>
      <Text style={styles.notificationTitle} numberOfLines={2}>
        {item.manhwaTitle}
      </Text>
      <Text style={styles.notificationSubtitle}>
        Last read: Chapter {item.lastChapterNumber}
      </Text>
      {item.hasNewChapters ? (
        <View style={styles.newChaptersBadge}>
          <Text style={styles.newChaptersText}>
            {item.newChaptersCount} new chapter{item.newChaptersCount > 1 ? 's' : ''}
          </Text>
        </View>
      ) : (
        <Text style={styles.noNewChaptersText}>No new chapters yet</Text>
      )}
    </View>
    <Ionicons name="chevron-forward" size={20} color="#A39DCE" />
  </TouchableOpacity>
);

const EmptyState = () => (
  <View style={styles.emptyState}>
    <Ionicons name="notifications-outline" size={64} color="#A39DCE" />
    <Text style={styles.emptyStateTitle}>No notifications</Text>
    <Text style={styles.emptyStateMessage}>
      You'll see notifications here when new chapters are available for your reading list.
    </Text>
  </View>
);

export default function NotificationsScreen({ navigation }) {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const insets = useSafeAreaInsets();

  const checkForNewChapters = async () => {
    try {
      const allProgress = await storageService.getAllReadingProgress();
      const progressArray = Object.values(allProgress);

      const notificationPromises = progressArray.map(async (progressItem) => {
        try {
          if (!progressItem || !progressItem.slug) {
            return null;
          }

          const comicData = await comickAPI.getComicDetails(progressItem.slug);
          if (!comicData?.comic?.hid) return null;

          const chaptersData = await comickAPI.getComicChapters(comicData.comic.hid, { 
            limit: 5000, 
            lang: 'en' 
          });
          
          const englishChapters = chaptersData.chapters || [];
          
          const uniqueChapterNumbers = new Set(englishChapters.map(c => c.chap).filter(Boolean));
          const currentTotalUniqueChapters = uniqueChapterNumbers.size;
          
          const storedTotalChapters = progressItem.totalChaptersOnRead || 0;

          const newChaptersCount = Math.max(0, currentTotalUniqueChapters - storedTotalChapters);
          const hasNewChapters = newChaptersCount > 0;

          // FIX: Self-healing logic. If new chapters are found, it means the stored
          // snapshot was out of date. We update it now so the notification won't
          // appear again incorrectly on the next refresh.
          if (hasNewChapters) {
            const highestChapterNumber = Math.max(
              ...[...uniqueChapterNumbers].map(c => parseFloat(c)).filter(num => !isNaN(num)), 0
            );
            // Update the storage with the new, correct total count.
            await storageService.updateReadingProgress(progressItem.slug, {
              ...progressItem,
              totalChaptersOnRead: currentTotalUniqueChapters,
              highestChapterNumber: highestChapterNumber,
            });
          }

          return {
            ...progressItem,
            hid: comicData.comic.hid,
            newChaptersCount,
            hasNewChapters,
          };
        } catch (error) {
          if (error.response?.status !== 404) {
             console.error(`Error checking chapters for ${progressItem?.slug}:`, error);
          }
          return null;
        }
      });

      const notificationResults = await Promise.all(notificationPromises);
      const validNotifications = notificationResults.filter(item => item !== null);
      
      validNotifications.sort((a, b) => {
        if (a.hasNewChapters && !b.hasNewChapters) return -1;
        if (!a.hasNewChapters && b.hasNewChapters) return 1;
        return new Date(b.lastReadAt) - new Date(a.lastReadAt);
      });

      setNotifications(validNotifications);
    } catch (error) {
      console.error('Error during notification check process:', error);
      Alert.alert('Error', 'Failed to check for new chapters. Please try again.');
    }
  };

  const loadData = async () => {
    setLoading(true);
    await checkForNewChapters();
    setLoading(false);
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadData();
    });
    return unsubscribe;
  }, [navigation]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    checkForNewChapters().finally(() => setRefreshing(false));
  }, []);

  const navigateToManhwaDetail = (item) => {
    navigation.navigate('ManhwaDetail', { 
      slug: item.slug, 
      hid: item.hid,
      title: item.manhwaTitle 
    });
  };

  if (loading && !refreshing) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <View style={[styles.header, { paddingTop: insets.top }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="#FFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
          <View style={styles.placeholder} />
        </View>
        <ActivityIndicator size="large" color="#FFF" style={{ marginTop: 100 }} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#FFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        <View style={styles.placeholder} />
      </View>
      <FlatList
        data={notifications}
        renderItem={({ item }) => <NotificationItem item={item} onPress={navigateToManhwaDetail} />}
        keyExtractor={(item) => item.slug}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#FFF" />}
        ListEmptyComponent={<EmptyState />}
        contentContainerStyle={styles.listContent}
        initialNumToRender={10}
        maxToRenderPerBatch={8}
        updateCellsBatchingPeriod={50}
        windowSize={11}
        removeClippedSubviews
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0F0F12',
  },
  loadingContainer: {
    justifyContent: 'flex-start',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: '#0F0F12',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.05)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(30,30,36,0.6)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.05)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
  },
  placeholder: {
    width: 40,
  },
  listContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    flexGrow: 1,
  },
  notificationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(30,30,36,0.6)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  notificationImage: {
    width: 60,
    height: 90,
    borderRadius: 10,
    backgroundColor: 'rgba(30,30,36,0.6)',
  },
  notificationContent: {
    flex: 1,
    marginLeft: 16,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFF',
    marginBottom: 4,
  },
  notificationSubtitle: {
    fontSize: 14,
    color: '#A39DCE',
    marginBottom: 8,
  },
  newChaptersBadge: {
    backgroundColor: '#4CAF50',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
  },
  newChaptersText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFF',
  },
  noNewChaptersText: {
    fontSize: 12,
    color: '#8A8899',
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 80,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateMessage: {
    fontSize: 14,
    color: '#A39DCE',
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
  },
});