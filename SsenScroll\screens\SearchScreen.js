import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View, Text, StyleSheet, TextInput, FlatList, TouchableOpacity,
  Alert, ActivityIndicator, SafeAreaView, ScrollView, Modal, TouchableWithoutFeedback
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, { FadeIn, useSharedValue,useAnimatedProps, withRepeat, useAnimatedStyle, withTiming, Easing } from 'react-native-reanimated';
import * as NavigationBar from 'expo-navigation-bar';
import { comickAPI, getCoverImageUrl } from '../services/api';
import { FilterDrawer } from '../components/FilterDrawer';
import { LinearGradient } from 'expo-linear-gradient'; // <-- Import LinearGradient
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const SEARCH_HISTORY_KEY = '@search_history';

// --- NEW: PulsatingBorder component ---

const PulsatingBorder = ({ focused }) => {
  const rotation = useSharedValue(0);

  useEffect(() => {
    if (focused) {
      // Animate the rotation value from 0 to 360 degrees, and repeat.
      rotation.value = withRepeat(
        withTiming(360, { duration: 2000, easing: Easing.linear }),
        -1, // -1 means repeat indefinitely
        false
      );
    } else {
      // When not focused, just reset the rotation.
      rotation.value = withTiming(0, { duration: 200 });
    }
  }, [focused, rotation]);

  // Create an animated style object that Reanimated will update efficiently on the UI thread.
  const animatedStyle = useAnimatedStyle(() => {
    return {
      // We string-interpolate the rotation value into the transform property.
      transform: [{ rotate: `${rotation.value}deg` }],
    };
  });

  // Don't render anything if the input is not focused.
  if (!focused) {
    return null;
  }

  return (
    // The Animated.View is what gets rotated.
    // It is positioned absolutely to fill the search bar wrapper.
    <Animated.View style={[styles.pulsatingBorder, animatedStyle]}>
      {/*
        The LinearGradient is NOT animated directly. It's a static gradient
        that is placed inside the rotating view.
        The rotation of its parent is what creates the illusion of an orbiting light.
      */}
      <LinearGradient
        colors={['#A39DCE', 'transparent']}
        locations={[0.1, 0.6]} // Makes the head of the gradient small and bright.
        start={{ x: 0, y: 0.5 }}
        end={{ x: 1, y: 0.5 }}
        style={{
          width: '100%',
          height: '100%',
        }}
      />
    </Animated.View>
  );
};


const SearchBar = ({ query, onQueryChange, onClear, onSubmit, onBack, onFilter, areFiltersActive }) => {
    const [isFocused, setIsFocused] = useState(false);

    return (
        <View style={styles.searchBarRow}>
            <TouchableOpacity style={styles.backButton} onPress={onBack}><Ionicons name="arrow-back" size={24} color="#D0CFD4" /></TouchableOpacity>
            <View style={styles.searchBarWrapper}>
                <PulsatingBorder focused={isFocused} />
                <View style={styles.searchBarContainer}>
                    <Ionicons name="search" size={22} color="#8A8899" />
                    <TextInput
                        style={styles.searchInput}
                        placeholder="Search titles or filter genres"
                        placeholderTextColor="#8A8899"
                        value={query}
                        onChangeText={onQueryChange}
                        onSubmitEditing={onSubmit}
                        returnKeyType="search"
                        onFocus={() => setIsFocused(true)}
                        onBlur={() => setIsFocused(false)}
                    />
                    {query.length > 0 && (<TouchableOpacity onPress={onClear}><View style={styles.clearButton}><Ionicons name="close" size={16} color="#1F1D2B" /></View></TouchableOpacity>)}
                </View>
            </View>
            <TouchableOpacity style={styles.filterButton} onPress={onFilter}>
                <Ionicons name="options-outline" size={26} color={areFiltersActive ? "#A39DCE" : "#D0CFD4"} />
                {areFiltersActive && <View style={styles.filterActiveDot}/>}
            </TouchableOpacity>
        </View>
    );
};

const ChipSection = ({ title, items, onChipPress, onClear }) => {
    if (!items || items.length === 0) return null;
    return (
        <Animated.View entering={FadeIn.duration(400)} style={styles.section}>
            <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>{title}</Text>
                {onClear && (<TouchableOpacity onPress={onClear}><Text style={styles.clearAllText}>clear all</Text></TouchableOpacity>)}
            </View>
            <View style={styles.chipContainer}>
                {items.map((term, index) => (<TouchableOpacity key={`${term}-${index}`} style={styles.chip} onPress={() => onChipPress(term)}><Text style={styles.chipText}>{term}</Text></TouchableOpacity>))}
            </View>
        </Animated.View>
    );
};

const SearchResultItem = React.memo(({ item, onNavigate }) => (
    <TouchableOpacity style={styles.resultItem} onPress={() => onNavigate(item)}>
        <Image source={{ uri: getCoverImageUrl(item.md_covers?.[0]) }} style={styles.resultImage} contentFit="cover" placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj[" />
        <View style={styles.resultInfo}>
            <Text style={styles.resultTitle} numberOfLines={2}>{item.title}</Text>
            <Text style={styles.resultAuthor}><Ionicons name="people" size={14} color="#FF6B6B"/> {item.user_follow_count.toLocaleString()}</Text>
            <Text style={styles.resultDesc} numberOfLines={2}>{item.desc?.trim() || 'No description provided.'}</Text>
        </View>
    </TouchableOpacity>
));

const EmptyState = ({ title, message }) => (
    <View style={styles.emptyStateContainer}><Ionicons name="search-circle-outline" size={80} color="#2D2A41" /><Text style={styles.emptyStateTitle}>{title}</Text><Text style={styles.emptyStateMessage}>{message}</Text></View>
);

const SortDropdown = ({ isVisible, onClose, onSelectSort, position }) => {
    if (!isVisible) return null;
    const options = [
        { key: 'desc', label: 'Followers (High to Low)' },
        { key: 'asc', label: 'Followers (Low to High)' },
        { key: null, label: 'None' },
    ];
    return (
        <Modal transparent={true} visible={isVisible} animationType="fade" onRequestClose={onClose}>
            <TouchableWithoutFeedback onPress={onClose}>
                <View style={StyleSheet.absoluteFill}>
                    <Animated.View entering={FadeIn.duration(150)} style={[styles.dropdown, { top: position.y + position.height + 5, right: 20 }]}>
                        {options.map(opt => (
                            <TouchableOpacity key={opt.key} style={styles.dropdownOption} onPress={() => onSelectSort(opt.key)}>
                                <Text style={styles.dropdownOptionText}>{opt.label}</Text>
                            </TouchableOpacity>
                        ))}
                    </Animated.View>
                </View>
            </TouchableWithoutFeedback>
        </Modal>
    );
};


export default function SearchScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const [loadingMore, setLoadingMore] = useState(false);
  const [allResultsLoaded, setAllResultsLoaded] = useState(false);
  const currentPage = useRef(1);
  const currentSearchParams = useRef(null);

  const [searchHistory, setSearchHistory] = useState([]);
  const [popularSuggestions, setPopularSuggestions] = useState([]);

  const [isFilterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const [activeFilters, setActiveFilters] = useState({ genres: [], tags: [], time: null, sort: 'follow' });
  const hasActiveFiltersValue = (activeFilters.genres && activeFilters.genres.length > 0) || (activeFilters.tags && activeFilters.tags.length > 0);

  const [followerSort, setFollowerSort] = useState(null);
  const [isSortDropdownVisible, setSortDropdownVisible] = useState(false);
  const sortButtonRef = useRef(null);
  const [sortButtonPosition, setSortButtonPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });
    const isFocused = useIsFocused();


  // --- NEW LOGIC ---
  const handleDrawerShow = useCallback(async () => {
    // This is called when the modal is fully visible
    await NavigationBar.setBackgroundColorAsync('#1F1D2B'); // Drawer background color
  }, []);

  const handleDrawerClose = useCallback(async () => {
    // This is called to close the modal
    setFilterDrawerVisible(false);
    await NavigationBar.setBackgroundColorAsync('#1a1a1a'); // Reset to default app color
  }, []);

  // This ensures the color is correct when you navigate to the screen
  useFocusEffect(
    useCallback(() => {
      NavigationBar.setBackgroundColorAsync('#1a1a1a');
      NavigationBar.setButtonStyleAsync('light');
    }, [])
  );
  // --- END NEW LOGIC ---


  const loadInitialData = async () => {
    try {
        const historyString = await AsyncStorage.getItem(SEARCH_HISTORY_KEY);
        if (historyString) setSearchHistory(JSON.parse(historyString));
        const trendingData = await comickAPI.getTrendingComics({ comic_types: 'manhwa' });
        if (trendingData && trendingData.rank) setPopularSuggestions(trendingData.rank.slice(0, 6).map(c => c.title));
    } catch(e) {
        console.error("Failed to load initial data", e);
    }
  };

  useFocusEffect(useCallback(() => { loadInitialData(); }, []));

  useEffect(() => {
    if (!hasSearched) return;
    if (followerSort === 'asc') return;

    const newParams = { ...currentSearchParams.current };

    if (followerSort === 'desc') {
        newParams.sort = 'user_follow_count';
    } else {
        newParams.sort = activeFilters.sort || 'follow';
    }

    if (newParams.sort !== currentSearchParams.current?.sort) {
       handleNewSearch(newParams);
    }
  }, [followerSort]);

  const saveSearchHistory = async (term) => {
    try {
      const newHistory = [term, ...searchHistory.filter(t => t.toLowerCase() !== term.toLowerCase())].slice(0, 5);
      setSearchHistory(newHistory);
      await AsyncStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory));
    } catch (e) { console.error("Failed to save history.", e); }
  };

  const clearSearchHistory = async () => {
    try {
        setSearchHistory([]);
        await AsyncStorage.removeItem(SEARCH_HISTORY_KEY);
    } catch (e) { console.error("Failed to clear history.", e); }
  };

  const handleClear = () => {
    setSearchQuery('');
    setSearchResults([]);
    setHasSearched(false);
    setActiveFilters({ genres: [], tags: [], time: null, sort: 'follow' });
    setFollowerSort(null);
    currentSearchParams.current = null;
  };

  const handleNewSearch = async (params) => {
    setLoading(true);
    setHasSearched(true);
    setSearchResults([]);

    currentSearchParams.current = params;
    currentPage.current = 1;
    setAllResultsLoaded(false);

    try {
        const initialResults = await comickAPI.searchComics({ ...params, page: 1 });
        setSearchResults(initialResults || []);
        if (!initialResults || initialResults.length < 30) setAllResultsLoaded(true);
    } catch (error) {
        Alert.alert('Search Error', 'Failed to fetch results. Please try again.');
    } finally {
        setLoading(false);
    }
  };

  const handleLoadMore = async () => {
    if (loading || loadingMore || allResultsLoaded || !currentSearchParams.current || followerSort === 'asc') return;
    setLoadingMore(true);
    const nextPage = currentPage.current + 1;
    try {
        const newResults = await comickAPI.searchComics({ ...currentSearchParams.current, page: nextPage });
        if (newResults && newResults.length > 0) {
            setSearchResults(prev => [...prev, ...newResults]);
            currentPage.current = nextPage;
        } else {
            setAllResultsLoaded(true);
        }
    } catch (error) {
        console.error("Failed to load more results", error);
    } finally {
        setLoadingMore(false);
    }
  };

  const handleKeywordSearch = (query) => {
    const term = query.trim();
    if (!term) return;
    setActiveFilters({ genres: [], tags: [], time: null, sort: 'follow' });
    setFollowerSort(null);
    setSearchQuery(term);
    saveSearchHistory(term);
    handleNewSearch({ q: term });
  };

  const handleApplyFilters = (filters) => {
    // We use the new close handler here
    handleDrawerClose();
    setActiveFilters(filters);
    setFollowerSort(null);

    if (filters.genres.length === 0 && filters.tags.length === 0) {
        if(searchQuery) handleNewSearch({ q: searchQuery });
        return;
    }

    setSearchQuery('');
    const searchParams = {
        genres: filters.genres,
        tags: filters.tags,
        sort: filters.sort,
        time: filters.time,
    };
    handleNewSearch(searchParams);
  };

  const onSelectFollowerSort = (option) => {
    setSortDropdownVisible(false);

    if (option === 'asc') {
        Alert.alert(
            "Sorting Limitation",
            "Sorting by lowest followers only applies to currently loaded results and disables loading more.",
            [{ text: "OK" }]
        );
        setSearchResults(prevResults => [...prevResults].sort((a, b) => a.user_follow_count - b.user_follow_count));
    }
    
    setFollowerSort(option);
  };

  const navigateToDetail = (comic) => navigation.navigate('ManhwaDetail', { slug: comic.slug, hid: comic.hid, title: comic.title });
  
  const renderFooter = () => (loadingMore ? <ActivityIndicator style={{ marginVertical: 20 }} size="large" color="#FFF" /> : null);

  const PreSearchContent = () => (
      <ScrollView>
        <ChipSection title="Last search" items={searchHistory} onClear={clearSearchHistory} onChipPress={handleKeywordSearch} />
        <ChipSection title="Popular Right Now" items={popularSuggestions} onChipPress={handleKeywordSearch} />
      </ScrollView>
  );

  const getSortButtonLabel = () => {
    if (followerSort === 'desc') return 'Followers (High-Low)';
    if (followerSort === 'asc') return 'Followers (Low-High)';
    return 'Sort by';
  };

  return (
    <SafeAreaView style={styles.container}>
      
        <View style={[styles.header, { paddingTop: insets.top + 6 }]}>
            <SearchBar
                query={searchQuery} onQueryChange={setSearchQuery} onClear={handleClear}
                onSubmit={() => handleKeywordSearch(searchQuery)}
                onBack={() => navigation.goBack()}
                onFilter={() => setFilterDrawerVisible(true)} // This just opens the modal
                areFiltersActive={hasActiveFiltersValue}
            />
        </View>

        <View style={styles.content}>
            {loading ? <ActivityIndicator style={{ marginTop: 50 }} size="large" color="#FFF" />
            : hasSearched ? (
                <>
                    <View style={styles.resultsHeader}>
                        <Text style={styles.resultsHeaderText} numberOfLines={1}>
                            {searchQuery ? `Results for "${searchQuery}"` : 'Filtered Results'}
                        </Text>
                        <TouchableOpacity
                            ref={sortButtonRef}
                            onLayout={() => {
                                sortButtonRef.current?.measure((fx, fy, width, height, px, py) => {
                                    setSortButtonPosition({ x: px, y: py, width, height });
                                });
                            }}
                            style={styles.sortButton}
                            onPress={() => setSortDropdownVisible(true)}
                        >
                            <Ionicons name="funnel-outline" size={16} color="#A39DCE" />
                            <Text style={styles.sortButtonText}>{getSortButtonLabel()}</Text>
                        </TouchableOpacity>
                    </View>

                    <FlatList
                        data={searchResults}
                        renderItem={({ item }) => <SearchResultItem item={item} onNavigate={navigateToDetail} />}
                        keyExtractor={(item, index) => `${item.hid}-${index}`}
                        ListEmptyComponent={<EmptyState title={`No Results Found`} message={ hasActiveFiltersValue ? 'Try adjusting your filters.' : 'Try a different keyword.'} />}
                        onEndReached={handleLoadMore}
                        onEndReachedThreshold={0.7}
                        ListFooterComponent={renderFooter}
                        contentContainerStyle={styles.listContentContainer}
                        initialNumToRender={12}
                        maxToRenderPerBatch={8}
                        updateCellsBatchingPeriod={50}
                        windowSize={11}
                        removeClippedSubviews
                    />
                </>
            ) : ( <PreSearchContent /> )}
        </View>

        <SortDropdown
            isVisible={isSortDropdownVisible}
            onClose={() => setSortDropdownVisible(false)}
            onSelectSort={onSelectFollowerSort}
            position={sortButtonPosition}
        />

        <FilterDrawer
            isVisible={isFilterDrawerVisible}
            onClose={handleDrawerClose} // <-- Pass the new close handler
            onShow={handleDrawerShow}   // <-- Pass the new onShow handler
            onApply={handleApplyFilters}
            activeFilters={activeFilters}
        />
    </SafeAreaView>
  );
}


// --- UPDATED Styles ---
const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#0F0F12' },
  header: { paddingBottom: 10, borderBottomWidth: 1, borderBottomColor: 'rgba(255,255,255,0.05)' },
  searchBarRow: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: 15, paddingTop: 10 },
  backButton: { padding: 5, marginRight: 5 },
  // The wrapper now needs to be a positioning context for the border.
  searchBarWrapper: {
    flex: 1,
    borderRadius: 35,
    overflow: 'hidden',
    position: 'relative' // This is key for positioning the PulsatingBorder absolutely inside.
  },
  // The search bar itself is inset to reveal the gradient border underneath.
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1A1A1F',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.05)',
    height: 50,
    paddingHorizontal: 15,
    margin: 2,
    borderRadius: 33,
  },
  searchInput: { flex: 1, color: '#FFF', fontSize: 16, marginLeft: 10 },
  clearButton: { width: 24, height: 24, borderRadius: 12, backgroundColor: '#8A8899', justifyContent: 'center', alignItems: 'center' },
  filterButton: { padding: 5, marginLeft: 10, position: 'relative' },
  filterActiveDot: { position: 'absolute', right: 6, top: 6, width: 9, height: 9, borderRadius: 5, backgroundColor: '#38A3A5', borderWidth: 1, borderColor: '#1F1D2B' },
  content: { flex: 1 },
  listContentContainer: { paddingHorizontal: 20, paddingTop: 10, paddingBottom: 30 },
  resultsHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 20, paddingVertical: 10, },
  resultsHeaderText: { color: '#8A8899', fontSize: 14, fontWeight: '500', flexShrink: 1, marginRight: 10 },
  sortButton: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#2D2A41', paddingVertical: 8, paddingHorizontal: 12, borderRadius: 20 },
  sortButtonText: { color: '#A39DCE', fontSize: 14, fontWeight: '600', marginLeft: 6 },
  dropdown: { position: 'absolute', backgroundColor: '#2D2A41', borderRadius: 10, borderWidth: 1, borderColor: '#3a3753', shadowColor: '#000', shadowOffset: { width: 0, height: 4 }, shadowOpacity: 0.3, shadowRadius: 5, elevation: 8, },
  dropdownOption: { paddingVertical: 12, paddingHorizontal: 20, },
  dropdownOptionText: { color: '#D0CFD4', fontSize: 15, fontWeight: '500' },
  section: { marginTop: 25, paddingHorizontal: 20 },
  sectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 15 },
  sectionTitle: { color: '#FFF', fontSize: 20, fontWeight: 'bold' },
  clearAllText: { color: '#A39DCE', fontSize: 14, fontWeight: '600' },
  chipContainer: { flexDirection: 'row', flexWrap: 'wrap' },
  chip: { backgroundColor: 'rgba(45, 42, 65, 0.8)', paddingVertical: 8, paddingHorizontal: 16, borderRadius: 20, marginRight: 10, marginBottom: 10 },
  chipText: { color: '#D0CFD4', fontSize: 14, fontWeight: '500' },
  emptyStateContainer:{ flex: 1, alignItems: 'center', justifyContent: 'center', paddingBottom: 50 },
  emptyStateTitle: { color: '#FFF', fontSize: 18, fontWeight: 'bold', marginTop: 15 },
  emptyStateMessage: { color: '#8A8899', fontSize: 15, textAlign: 'center', marginTop: 8, paddingHorizontal: 20 },
  resultItem: { flexDirection: 'row', marginBottom: 14, backgroundColor: 'rgba(30,30,36,0.6)', borderWidth: 1, borderColor: 'rgba(255,255,255,0.05)', borderRadius: 12, padding: 12 },
  resultImage: { width: 80, height: 120, borderRadius: 10, backgroundColor: 'rgba(30,30,36,0.6)' },
  resultInfo: { flex: 1, marginLeft: 15, justifyContent: 'center' },
  resultTitle: { color: '#FFF', fontSize: 17, fontWeight: '600', marginBottom: 4 },
  resultAuthor: { color: '#A39DCE', fontSize: 14, marginBottom: 8, display: 'flex', alignItems: 'center' },
  resultDesc: { color: '#8A8899', fontSize: 13, lineHeight: 18 },
  // The pulsating border is now a layer that fills its wrapper.
  pulsatingBorder: {
    ...StyleSheet.absoluteFillObject,
  },
  // The 'pulsatingLight' style is no longer used and has been removed.
});