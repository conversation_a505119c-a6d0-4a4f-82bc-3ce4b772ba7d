import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { storageService } from '../services/storageService';

const SettingsScreen = ({ navigation }) => {
  const [settings, setSettings] = useState({
    darkMode: false,
    readerBackgroundColor: '#000000',
    defaultChapterSortOrder: 'asc',
    autoScrollSpeed: 100,
    preloadNextChapter: true,
    pushNotifications: true,
    imageQuality: 'medium',
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await storageService.getSettings();
      setSettings(savedSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const updateSetting = async (key, value) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      await storageService.updateSettings({ [key]: value });
    } catch (error) {
      console.error('Error updating setting:', error);
    }
  };

  const handleResetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await storageService.resetSettings();
              const defaultSettings = await storageService.getSettings();
              setSettings(defaultSettings);
            } catch (error) {
              console.error('Error resetting settings:', error);
            }
          },
        },
      ]
    );
  };

  const handleClearCache = () => {
    Alert.alert(
      'Clear Cache',
      'This will clear all cached images and temporary data. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            // In a real app, this would clear image cache
            Alert.alert('Cache Cleared', 'Image cache has been cleared.');
          },
        },
      ]
    );
  };

  const handleClearReadingHistory = () => {
    Alert.alert(
      'Clear Reading History',
      'This will remove all reading progress. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await storageService.clearAllReadingProgress();
              Alert.alert('History Cleared', 'All reading progress has been cleared.');
            } catch (error) {
              console.error('Error clearing reading history:', error);
            }
          },
        },
      ]
    );
  };

  const renderSettingItem = (label, key, type = 'switch') => {
    const value = settings[key];

    if (type === 'switch') {
      return (
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>{label}</Text>
          <Switch
            value={value}
            onValueChange={(newValue) => updateSetting(key, newValue)}
            trackColor={{ false: '#767577', true: '#007AFF' }}
            thumbColor={value ? '#fff' : '#f4f3f4'}
          />
        </View>
      );
    }

    return null;
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          {renderSettingItem('Dark Mode', 'darkMode')}
          {renderSettingItem('Preload Next Chapter', 'preloadNextChapter')}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Reading Experience</Text>
          {renderSettingItem('Default Chapter Sort (Ascending)', 'defaultChapterSortOrder')}
          {renderSettingItem('Push Notifications', 'pushNotifications')}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleClearCache}>
            <Ionicons name="trash-outline" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Clear Cache</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleClearReadingHistory}>
            <Ionicons name="time-outline" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Clear Reading History</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleResetSettings}>
            <Ionicons name="refresh-outline" size={20} color="#ff3b30" />
            <Text style={[styles.actionButtonText, { color: '#ff3b30' }]}>Reset All Settings</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Version</Text>
            <Text style={styles.infoValue}>1.0.0</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Build</Text>
            <Text style={styles.infoValue}>100</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  backButton: {
    padding: 8,
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  section: {
    marginVertical: 16,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 12,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#2a2a2a',
    borderRadius: 8,
    marginBottom: 8,
  },
  settingLabel: {
    fontSize: 16,
    color: '#fff',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#2a2a2a',
    borderRadius: 8,
    marginBottom: 8,
  },
  actionButtonText: {
    fontSize: 16,
    color: '#fff',
    marginLeft: 12,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#2a2a2a',
    borderRadius: 8,
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 16,
    color: '#fff',
  },
  infoValue: {
    fontSize: 16,
    color: '#888',
  },
});

export default SettingsScreen;