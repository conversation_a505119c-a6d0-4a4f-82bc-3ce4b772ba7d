import axios from 'axios';
import qs from 'qs';

const BASE_URL = 'https://api.comick.fun';

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
  },
  // Fix for array serialization
  paramsSerializer: params => {
    return qs.stringify(params, { arrayFormat: 'repeat' });
  }
});

// API service functions
export const comickAPI = {
  // Get trending comics
  getTrendingComics: async (params = {}) => {
    try {
      const response = await api.get('/top', {
        params: {
          type: 'trending',
          comic_types: 'manhwa',
          accept_mature_content: false,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching trending comics:', error);
      throw error;
    }
  },

  // Get latest chapters
  getLatestChapters: async (params = {}) => {
    try {
      const response = await api.get('/chapter/', {
        params: {
          type: 'manhwa',
          page: 1,
          limit: 20,
          order: 'hot',
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching latest chapters:', error);
      throw error;
    }
  },

  // Search comics (handles both keyword and filter search)
  searchComics: async (searchParams = {}) => {
    const finalParams = {
        limit: 30,
        ...searchParams,
    };
    try {
      const queryString = qs.stringify(finalParams, { arrayFormat: 'repeat' });
      // Logging the URL for dev purposes as requested
      console.log(`[ComickAPI] Searching URL: ${BASE_URL}/v1.0/search/?${queryString}`);

      const response = await api.get('/v1.0/search/', { params: finalParams });
      return response.data;
    } catch (error) {
      console.error('Error searching comics:', error);
      throw error;
    }
  },

  // Get the list of all genres
  getGenres: async () => {
    try {
      const response = await api.get('/genre/');
      return response.data;
    } catch(error) {
      console.error('Error fetching genres:', error);
      throw error;
    }
  },

  // NEW FUNCTION: Get the list of all tags/categories from /category/ endpoint
  getTags: async () => {
    try {
      const response = await api.get('/category/');
      return response.data;
    } catch(error) {
      console.error('Error fetching tags:', error);
      throw error;
    }
  },

  // NEW FUNCTION: Search for a tag by keyword, ex: /search/tag?k=post-apo
  searchTags: async (keyword) => {
    if (!keyword || !keyword.trim()) return [];
    try {
      const response = await api.get('/search/tag', { params: { k: keyword.trim() } });
      // Expected response: [{"k":"post-apocalyptic","v":"Post-Apocalyptic (383)"}]
      return response.data;
    } catch (error) {
      console.error(`Error searching tags with keyword "${keyword}":`, error);
      throw error;
    }
  },

  // Get completed series
  getCompletedComics: async (params = {}) => {
    try {
      const response = await api.get('/v1.0/search/', {
        params: {
          status: 2,
          comic_types: 'manhwa',
          limit: 15,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching completed comics:', error);
      throw error;
    }
  },

  // Get the highest-rated comics
  getHighestRatedComics: async (params = {}) => {
    try {
      const response = await api.get('/v1.0/search/', {
        params: {
          sort: 'rating',
          comic_types: 'manhwa',
          limit: 15,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching highest rated comics:', error);
      throw error;
    }
  },

  // Get comic details
  getComicDetails: async (slug) => {
    try {
      const response = await api.get(`/comic/${slug}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching comic details:', error);
      throw error;
    }
  },

  // Get comic chapters
  getComicChapters: async (hid, params = {}) => {
    try {
      const response = await api.get(`/comic/${hid}/chapters`, {
        params: {
          limit: 100,
          page: 1,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching comic chapters:', error);
      throw error;
    }
  },

  // Get chapter details
  getChapterDetails: async (hid) => {
    try {
      const response = await api.get(`/chapter/${hid}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching chapter details:', error);
      throw error;
    }
  },

  // Get chapter comments
  getChapterComments: async ({ hid, comicId, chap }) => {
    const params = {
      'comic-id': comicId,
      chap: chap,
      lang: 'en'
    };
    try {
      const queryString = qs.stringify(params);
      console.log(`[ComickAPI] Fetching comments: ${BASE_URL}/comment/chapter/${hid}?${queryString}`);

      const response = await api.get(`/comment/chapter/${hid}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching chapter comments for hid ${hid}:`, error);
       if (error.response) {
        console.error('Error Response:', error.response.data);
      }
      throw error;
    }
  },
};


export const getCoverImageUrl = (cover) => {
  if (!cover || !cover.b2key) return null;
  return `https://meo.comick.pictures/${cover.b2key}`;
};

export const getChapterImageUrl = (image) => {
  if (!image || !image.b2key) return null;
  return `https://meo.comick.pictures/${image.b2key}`;
};

export const formatGenres = (genres) => {
  return genres ? genres.slice(0, 3).join(', ') : '';
};

export const formatStatus = (status) => {
  const statusMap = { 1: 'Ongoing', 2: 'Completed', 3: 'Cancelled', 4: 'Hiatus' };
  return statusMap[status] || 'Unknown';
};

export const formatContentRating = (rating) => {
  const ratingMap = { safe: 'Safe', suggestive: 'Suggestive', erotica: 'Erotica' };
  return ratingMap[rating] || 'Unknown';
};

export const groupChaptersByScanlator = (chapters) => {
  const grouped = {};
  chapters.forEach(chapter => {
    const groupName = chapter.group_name?.[0] || 'Unknown';
    const lang = chapter.lang || 'en';
    const key = `${groupName}_${lang}`;
    if (!grouped[key]) {
      grouped[key] = { groupName, language: lang, chapters: [] };
    }
    grouped[key].chapters.push(chapter);
  });
  Object.values(grouped).forEach(group => {
    group.chapters.sort((a, b) => (parseFloat(b.chap) || 0) - (parseFloat(a.chap) || 0));
  });
  return grouped;
};

export const getLanguageDisplayName = (langCode) => {
  const langMap = {
    'en': 'English', 'es': 'Spanish', 'fr': 'French',
    'de': 'German', 'it': 'Italian', 'pt': 'Portuguese',
    'ru': 'Russian', 'ja': 'Japanese', 'ko': 'Korean',
    'zh': 'Chinese', 'ar': 'Arabic', 'th': 'Thai',
    'vi': 'Vietnamese', 'pl': 'Polish', 'tr': 'Turkish',
  };
  return langMap[langCode] || langCode.toUpperCase();
};