import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const FAVORITES_KEY = '@favorites';
const READING_PROGRESS_KEY = '@reading_progress';
const COLORIZER_SETTINGS_KEY = '@colorizer_settings';
const READER_SETTINGS_KEY = '@reader_settings';

/**
 * Storage service for managing app data
 */
export const storageService = {

  getFavorites: async () => {
    try {
      const favoritesString = await AsyncStorage.getItem(FAVORITES_KEY);
      return favoritesString ? JSON.parse(favoritesString) : [];
    } catch (error) {
      console.error('Error getting favorites:', error);
      return [];
    }
  },

  addToFavorites: async (manhwa) => {
    try {
      const favorites = await storageService.getFavorites();
      const isAlreadyFavorited = favorites.some(fav => fav.slug === manhwa.slug);
      if (isAlreadyFavorited) {
        return true;
      }
      const favoriteItem = {
        slug: manhwa.slug,
        hid: manhwa.hid,
        title: manhwa.title,
        cover: manhwa.md_covers?.[0] || manhwa.cover,
        status: manhwa.status,
        rating: manhwa.bayesian_rating,
        addedAt: new Date().toISOString(),
      };
      const updatedFavorites = [favoriteItem, ...favorites];
      await AsyncStorage.setItem(FAVORITES_KEY, JSON.stringify(updatedFavorites));
      return true;
    } catch (error) {
      console.error('Error adding to favorites:', error);
      return false;
    }
  },

  removeFromFavorites: async (slug) => {
    try {
      const favorites = await storageService.getFavorites();
      const updatedFavorites = favorites.filter(fav => fav.slug !== slug);
      await AsyncStorage.setItem(FAVORITES_KEY, JSON.stringify(updatedFavorites));
      return true;
    } catch (error) {
      console.error('Error removing from favorites:', error);
      return false;
    }
  },

  isFavorited: async (slug) => {
    try {
      const favorites = await storageService.getFavorites();
      return favorites.some(fav => fav.slug === slug);
    } catch (error) {
      console.error('Error checking favorite status:', error);
      return false;
    }
  },

  toggleFavorite: async (manhwa) => {
    try {
      const isCurrentlyFavorited = await storageService.isFavorited(manhwa.slug);
      if (isCurrentlyFavorited) {
        await storageService.removeFromFavorites(manhwa.slug);
        return false;
      } else {
        await storageService.addToFavorites(manhwa);
        return true;
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      return false;
    }
  },



  getAllReadingProgress: async () => {
    try {
      const progressString = await AsyncStorage.getItem(READING_PROGRESS_KEY);
      return progressString ? JSON.parse(progressString) : {};
    } catch (error) {
      console.error('Error getting reading progress:', error);
      return {};
    }
  },

  getReadingProgress: async (slug) => {
    try {
      const allProgress = await storageService.getAllReadingProgress();
      return allProgress[slug] || null;
    } catch (error) {
      console.error('Error getting reading progress for slug:', error);
      return null;
    }
  },

  updateReadingProgress: async (slug, progressData) => {
    try {
      const allProgress = await storageService.getAllReadingProgress();
      
      const lastReadNum = parseFloat(progressData.lastChapterNumber);
      const highestChapNum = parseFloat(progressData.highestChapterNumber);

      const progressItem = {
        slug: slug,
        ...progressData,
        lastReadAt: new Date().toISOString(),
        progressPercentage: highestChapNum > 0 && !isNaN(lastReadNum)
          ? Math.round((lastReadNum / highestChapNum) * 100)
          : 0,
      };

      allProgress[slug] = progressItem;
      await AsyncStorage.setItem(READING_PROGRESS_KEY, JSON.stringify(allProgress));
      return true;
    } catch (error) {
      console.error('Error updating reading progress:', error);
      return false;
    }
  },

  getRecentlyRead: async (limit = 10) => {
    try {
      const allProgress = await storageService.getAllReadingProgress();
      const progressArray = Object.values(allProgress);

      progressArray.sort((a, b) => new Date(b.lastReadAt) - new Date(a.lastReadAt));
      
      return progressArray.slice(0, limit);
    } catch (error) {
      console.error('Error getting recently read:', error);
      return [];
    }
  },

  removeReadingProgress: async (slug) => {
    try {
      const allProgress = await storageService.getAllReadingProgress();
      delete allProgress[slug];
      await AsyncStorage.setItem(READING_PROGRESS_KEY, JSON.stringify(allProgress));
      return true;
    } catch (error) {
      console.error('Error removing reading progress:', error);
      return false;
    }
  },

 

  getReaderSettings: async () => {
    try {
      const settingsString = await AsyncStorage.getItem(READER_SETTINGS_KEY);
      return settingsString ? JSON.parse(settingsString) : null;
    } catch (e) {
      console.error("Failed to get reader settings.", e);
      return null;
    }
  },

  saveReaderSettings: async (settings) => {
    try {
      await AsyncStorage.setItem(READER_SETTINGS_KEY, JSON.stringify(settings));
      return true;
    } catch (e) {
      console.error("Failed to save reader settings.", e);
      return false;
    }
  },
  
  getColorizerSettings: async () => {
      try {
          const settingsString = await AsyncStorage.getItem(COLORIZER_SETTINGS_KEY);
          return settingsString ? JSON.parse(settingsString) : null;
      } catch (e) {
          console.error("Failed to get colorizer settings.", e);
          return null;
      }
  },

  saveColorizerSettings: async (settings) => {
      try {
          await AsyncStorage.setItem(COLORIZER_SETTINGS_KEY, JSON.stringify(settings));
          return true;
      } catch (e) {
          console.error("Failed to save colorizer settings.", e);
          return false;
      }
  },

  // --- UTILITY METHODS ---

  clearAllFavorites: async () => {
    try {
      await AsyncStorage.removeItem(FAVORITES_KEY);
      return true;
    } catch (error) {
      console.error('Error clearing favorites:', error);
      return false;
    }
  },

  clearAllReadingProgress: async () => {
    try {
      await AsyncStorage.removeItem(READING_PROGRESS_KEY);
      return true;
    } catch (error)
    {
      console.error('Error clearing reading progress:', error);
      return false;
    }
  },

  getStorageStats: async () => {
    try {
      const [favorites, progress] = await Promise.all([
        storageService.getFavorites(),
        storageService.getAllReadingProgress(),
      ]);

      return {
        favoritesCount: favorites.length,
        progressCount: Object.keys(progress).length,
        totalStorageItems: favorites.length + Object.keys(progress).length,
      };
    } catch (error) {
      console.error('Error getting storage stats:', error);
      return {
        favoritesCount: 0,
        progressCount: 0,
        totalStorageItems: 0,
      };
    }
  },
};