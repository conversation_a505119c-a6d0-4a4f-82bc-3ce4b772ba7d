// Provider detection and metadata utilities
// Professional, minimal, and documented for maintainability.

// Map known provider hostnames to metadata. Fallback to 'site' for unknowns.
const PROVIDER_MAP = [
  {
    id: 'webtoons',
    hosts: ['www.webtoons.com', 'm.webtoons.com', 'webtoons.com'],
    name: 'WEBTOON',
    icon: { family: 'Ionicons', name: 'chatbubbles-outline', color: '#21C35E' },
  },
  {
    id: 'kakao',
    hosts: ['webtoon.kakao.com', 'page.kakao.com', 'kakaopage.com'],
    name: 'Kakao Webtoon',
    icon: { family: 'Ionicons', name: 'sparkles-outline', color: '#FFDD00' },
  },
  {
    id: 'naver',
    hosts: ['comic.naver.com', 'm.comic.naver.com'],
    name: 'Naver Comics',
    icon: { family: 'Ionicons', name: 'leaf-outline', color: '#00D564' },
  },
  {
    id: 'lezhin',
    hosts: ['www.lezhinus.com', 'lezhin.com'],
    name: '<PERSON><PERSON><PERSON>',
    icon: { family: 'Ionicons', name: 'heart-circle-outline', color: '#E60012' },
  },
  {
    id: 'mangadex',
    hosts: ['mangadex.org'],
    name: 'MangaDex',
    icon: { family: 'Ionicons', name: 'layers-outline', color: '#FF9900' },
  },
  {
    id: 'comick',
    hosts: ['comick.io', 'api.comick.fun'],
    name: 'Comick',
    icon: { family: 'Ionicons', name: 'book-outline', color: '#7B61FF' },
  },
];

const DEFAULT_PROVIDER = {
  id: 'site',
  name: 'Official Site',
  icon: { family: 'Ionicons', name: 'globe-outline', color: '#A1A1AA' },
};

/**
 * Extracts the hostname from a URL. Returns null on invalid input.
 * @param {string} url
 * @returns {string|null}
 */
export function extractHostname(url) {
  if (!url || typeof url !== 'string') return null;
  try {
    const u = new URL(url);
    return u.hostname.toLowerCase();
  } catch (_) {
    return null;
  }
}

/**
 * Returns provider metadata based on a URL or hostname.
 * @param {string} urlOrHost - Full URL or hostname.
 * @returns {{id:string,name:string,icon:{family:string,name:string,color:string}}}
 */
export function getProviderMeta(urlOrHost) {
  if (!urlOrHost) return DEFAULT_PROVIDER;
  const host = urlOrHost.includes('://') ? extractHostname(urlOrHost) : urlOrHost;
  if (!host) return DEFAULT_PROVIDER;
  const match = PROVIDER_MAP.find(p => p.hosts.some(h => host.endsWith(h)));
  return match || DEFAULT_PROVIDER;
}

/**
 * Attempts to detect the provider for a comic payload from Comick API.
 * @param {object} comic - The `comic` object from /comic/{slug}/ response
 * @returns {{id:string,name:string,icon:{family:string,name:string,color:string}}, url?:string}
 */
export function detectComicProvider(comic) {
  if (!comic) return DEFAULT_PROVIDER;
  const raw = comic?.links?.raw || comic?.links?.web || comic?.links?.official;
  if (raw) {
    const meta = getProviderMeta(raw);
    return { ...meta, url: raw };
  }
  // Fallback: derive from any external link
  const candidates = Object.values(comic?.links || {}).filter(Boolean);
  if (candidates.length > 0) {
    const meta = getProviderMeta(candidates[0]);
    return { ...meta, url: candidates[0] };
  }
  return DEFAULT_PROVIDER;
}

export const PROVIDERS = PROVIDER_MAP.map(p => ({ id: p.id, name: p.name }));

