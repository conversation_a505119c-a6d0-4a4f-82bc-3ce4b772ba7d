import requests
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from bs4 import BeautifulSoup, Tag
import uvicorn
from typing import List, Dict, Any
from fastapi.middleware.cors import CORSMiddleware

# --- API Initialization ---
app = FastAPI(
    title="FreeWebNovel Scraper API",
    description="An API to scrape the homepage, popular list, novel details, and chapter content from freewebnovel.com.",
    version="3.0.0",
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins (for development)
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods (GET, POST, etc.)
    allow_headers=["*"],  # Allows all headers
)

BASE_URL = "https://freewebnovel.com"

# --- Reusable Helper Parsing Functions ---
# These functions help parse different types of novel blocks found on the site.

def parse_novel_block(novel_tag: Tag) -> Dict[str, Any]:
    """
    Generic parser for novels in a block format (used for Featured, Latest, Completed, and Popular).
    """
    title_tag = novel_tag.select_one("h3.tit a")
    img_tag = novel_tag.select_one(".pic img")
    rating_tag = novel_tag.select_one(".core span")
    language_tag = novel_tag.select_one("em.e1 a") or novel_tag.select_one("em.e1")
    genre_tags = novel_tag.select(".desc .item a.novel")
    chapters_tag = novel_tag.select_one("a.chapter")

    status = "Ongoing"
    if chapters_tag and chapters_tag.find('span', class_='s2'):
        status = "Completed"
    
    chapter_info = ' '.join(span.text.strip() for span in chapters_tag.find_all('span')) if chapters_tag else "N/A"
    genres = [g.get("title", "").replace("Novels", "").strip() for g in genre_tags]
    language_text = language_tag.get("title", language_tag.text).replace("Novel", "").strip() if language_tag else "N/A"

    return {
        "title": title_tag.get("title", "N/A").strip() if title_tag else "N/A",
        "url": f"{BASE_URL}{title_tag.get('href', '')}" if title_tag else None,
        "image": f"{BASE_URL}{img_tag.get('src', '')}" if img_tag and img_tag.has_attr('src') else None,
        "rating": rating_tag.text.strip() if rating_tag else "N/A",
        "language": language_text,
        "genres": genres,
        "chapter_info": chapter_info,
        "status": status,
    }

def parse_latest_release(novel_tag: Tag) -> Dict[str, Any]:
    """
    Specific parser for the 'Latest Release' section's list format.
    """
    title_tag = novel_tag.select_one(".s1 a.tit")
    img_tag = novel_tag.select_one(".s1 .pic img")
    genre_tags = novel_tag.select(".s2 a")
    latest_chapter_tag = novel_tag.select_one(".s3 a")
    release_time_tag = novel_tag.select_one(".s4")
    genres = [g.get("title", "").replace("Novels", "").strip() for g in genre_tags]

    return {
        "title": title_tag.get("title", "N/A").strip() if title_tag else "N/A",
        "url": f"{BASE_URL}{title_tag.get('href', '')}" if title_tag else None,
        "image": f"{BASE_URL}{img_tag.get('src', '')}" if img_tag and img_tag.has_attr('src') else None,
        "genres": genres,
        "latest_chapter_title": latest_chapter_tag.get("title", "N/A").strip() if latest_chapter_tag else "N/A",
        "latest_chapter_url": f"{BASE_URL}{latest_chapter_tag.get('href', '')}" if latest_chapter_tag else None,
        "release_time": release_time_tag.text.strip() if release_time_tag else "N/A",
    }
    
def parse_nominated_novel(novel_tag: Tag) -> Dict[str, Any]:
    """
    Specific parser for the 'Nominated Novels' sidebar.
    """
    title_tag = novel_tag.select_one("a.con")
    img_tag = novel_tag.select_one(".pic img")
    genre_tags = novel_tag.select(".chitiet .s2 a")
    chapter_tag = novel_tag.select_one(".chitiet .s3")
    
    status_tag = novel_tag.select_one(".item span")
    status = "Ongoing"
    if status_tag:
        if "FULL" in status_tag.text: status = "Completed"
        elif "HOT" in status_tag.text: status = "Hot"
    
    return {
        "title": title_tag.get("title", "N/A").strip() if title_tag else "N/A",
        "url": f"{BASE_URL}{title_tag.get('href', '')}" if title_tag else None,
        "image": f"{BASE_URL}{img_tag.get('src', '')}" if img_tag and img_tag.has_attr('src') else None,
        "genres": [g.get("title", "").replace("Novels", "").strip() for g in genre_tags],
        "chapter_info": chapter_tag.text.strip() if chapter_tag else "N/A",
        "status": status,
    }

# --- API Endpoints ---

@app.get("/home", summary="Scrape the entire homepage")
def scrape_full_homepage():
    try:
        response = requests.get(f"{BASE_URL}/home")
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=503, detail=f"Could not retrieve content from the website: {e}")
        
    soup = BeautifulSoup(response.content, 'html.parser')
    
    featured_novels, latest_release_novels, latest_added_novels, completed_novels = [], [], [], []

    # Scrape Featured Novels
    featured_container = soup.find('div', class_='m-book')
    if featured_container:
        for item in featured_container.select('.col-l .li, .col-r .li'):
            featured_novels.append(parse_novel_block(item))

    # Scrape other sections
    for section in soup.find_all('div', class_='m-newest'):
        header_tag = section.select_one(".g-tit h3.tit")
        if not header_tag: continue
        
        header_text = header_tag.text.strip()
        if "Latest Release Novels" in header_text:
            for item in section.select("ul.ul-list2 li"):
                latest_release_novels.append(parse_latest_release(item))
        elif "Latest Novels" in header_text:
            for item in section.select(".rec .ul-list1-1 .li"):
                latest_added_novels.append(parse_novel_block(item))
        elif "Completed Novels" in header_text:
            for item in section.select(".rec .ul-list1-1 .li"):
                completed_novels.append(parse_novel_block(item))

    if not any([featured_novels, latest_release_novels, latest_added_novels, completed_novels]):
        raise HTTPException(status_code=404, detail="Could not find any target sections on the homepage.")

    return {
        "featured_novels": featured_novels,
        "latest_release_novels": latest_release_novels,
        "latest_novels": latest_added_novels,
        "completed_novels": completed_novels,
    }

@app.get("/popular", summary="Scrape the Most Popular page")
def scrape_popular_page():
    url = f"{BASE_URL}/sort/most-popular"
    try:
        response = requests.get(url)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=503, detail=f"Could not retrieve content from {url}: {e}")
        
    soup = BeautifulSoup(response.content, 'html.parser')
    
    popular_novels, nominated_novels = [], []

    # Scrape Main Most Popular List
    popular_container = soup.select_one(".ul-list1-2.ss-custom")
    if popular_container:
        for item in popular_container.find_all("div", class_="li-row"):
            popular_novels.append(parse_novel_block(item))
            
    # Scrape Sidebar Nominated Novels
    nominated_container = soup.select_one(".col-slide")
    if nominated_container:
        for header in nominated_container.find_all('h3', class_='tit'):
            if "Nominated Novels" in header.text:
                novel_list_container = header.find_next_sibling('ul', class_='ul-list6')
                if novel_list_container:
                    for item in novel_list_container.find_all('li'):
                        nominated_novels.append(parse_nominated_novel(item))
                break 

    if not popular_novels:
        raise HTTPException(status_code=404, detail="Could not find the 'Most Popular Novels' list.")

    return {
        "popular_novels": popular_novels,
        "nominated_novels": nominated_novels,
    }

@app.get("/novel/{novel_id}", summary="Scrape a specific novel page")
def scrape_novel_page(novel_id: str):
    url = f"{BASE_URL}/novel/{novel_id}"
    try:
        response = requests.get(url)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=503, detail=f"Could not retrieve content from {url}: {e}")

    soup = BeautifulSoup(response.content, 'html.parser')
    
    info_container = soup.select_one(".m-book1")
    if not info_container:
        raise HTTPException(status_code=404, detail=f"Could not find novel information for '{novel_id}'.")

    details = {
        "title": info_container.select_one("h1.tit").text.strip() if info_container.select_one("h1.tit") else "N/A",
        "image": f"{BASE_URL}{info_container.select_one('.pic img')['src']}" if info_container.select_one('.pic img') else None,
        "rating": info_container.select_one("p.vote").text.split('(')[0].strip() if info_container.select_one("p.vote") else "N/A",
        "summary": info_container.select_one(".m-desc .txt .inner").text.strip() if info_container.select_one(".m-desc .txt .inner") else "N/A",
        "metadata": {}
    }
    
    for item in info_container.select(".txt .item"):
        key_element = item.find("span", class_="glyphicon")
        if not key_element: continue
        
        key = key_element.get("title", "").strip().lower()
        value_elements = item.select(".right a")
        
        if value_elements:
            details["metadata"][key] = [{"text": el.text.strip(), "url": f"{BASE_URL}{el['href']}"} for el in value_elements]
        else:
            value_element = item.select_one(".right")
            if value_element:
                details["metadata"][key] = value_element.text.strip()

    latest_chapters = []
    latest_container = soup.select_one(".m-newest1 ul.ul-list5")
    if latest_container:
        for chapter_li in latest_container.find_all("li"):
            link = chapter_li.find("a")
            if link:
                latest_chapters.append({
                    "title": link.get("title", "N/A"),
                    "url": f"{BASE_URL}{link.get('href', '')}"
                })

    all_chapters = []
    chapters_container = soup.select_one("#idData")
    if chapters_container:
        for chapter_li in chapters_container.find_all("li"):
            link = chapter_li.find("a")
            if link:
                all_chapters.append({
                    "title": link.get("title", "N/A"),
                    "url": f"{BASE_URL}{link.get('href', '')}"
                })
                
    return {
        "details": details,
        "latest_chapters": latest_chapters,
        "all_chapters": all_chapters
    }

@app.get("/novel/{novel_id}/{chapter_id}", summary="Scrape a novel chapter page")
def scrape_chapter_page(novel_id: str, chapter_id: str):
    url = f"{BASE_URL}/novel/{novel_id}/{chapter_id}"
    try:
        response = requests.get(url)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=503, detail=f"Could not retrieve content from {url}: {e}")

    soup = BeautifulSoup(response.content, 'html.parser')

    read_container = soup.select_one(".m-read")
    content_container = soup.select_one("div#article")
    
    if not read_container or not content_container:
        raise HTTPException(status_code=404, detail=f"Could not find chapter content for '{novel_id}/{chapter_id}'.")

    novel_title_tag = read_container.select_one(".top h1.tit a")
    chapter_title_tag = read_container.select_one(".top span.chapter")
    
    paragraphs = [p.get_text(strip=True) for p in content_container.find_all("p")]
    content = "\n\n".join(paragraphs)

    prev_link_tag = read_container.select_one("#prev_url")
    next_link_tag = read_container.select_one("#next_url")

    next_chapter_url = next_link_tag.get('href') if next_link_tag else None
    if next_chapter_url and "/novel/" in next_chapter_url and "/chapter-" not in next_chapter_url:
        next_chapter_url = None

    navigation = {
        "previous_chapter": f"{BASE_URL}{prev_link_tag.get('href')}" if prev_link_tag and prev_link_tag.get('href') else None,
        "next_chapter": f"{BASE_URL}{next_chapter_url}" if next_chapter_url else None,
        "index_page": f"{BASE_URL}{novel_title_tag.get('href')}" if novel_title_tag else None
    }

    return {
        "novel_title": novel_title_tag.text.strip() if novel_title_tag else "N/A",
        "chapter_title": chapter_title_tag.text.strip() if chapter_title_tag else "N/A",
        "content": content,
        "navigation": navigation
    }

# --- Main entry point to run the server ---
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)