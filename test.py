import base64
import requests
import os
import time

# Image path
image_path = "images/ch1.png"
filename = os.path.basename(image_path)

# Encode image as base64 with prefix
with open(image_path, "rb") as image_file:
    base64_image = "data:image/png;base64," + base64.b64encode(image_file.read()).decode()

# Payload
payload = {
    "imgName": filename,
    "imgData": base64_image,
    "colorize": True,
    "upscale": False,
    "denoise": False,
    "cache": True  # Enables caching
}

# Start timing
start_time = time.time()

try:
    # Send POST request
    response = requests.post(
        "https://3827b71cccd3.ngrok-free.app/colorize-image-data",
        json=payload,
        headers={"Content-Type": "application/json"}
    )

    # Check response
    if response.status_code == 200:
        data = response.json()
        color_img_data = data.get("colorImgData")

        if color_img_data and "," in color_img_data:
            base64_data = color_img_data.split(",")[1]
            output_path = f"output/colorized_{filename}"
            os.makedirs("output", exist_ok=True)
            with open(output_path, "wb") as out_file:
                out_file.write(base64.b64decode(base64_data))
            print(f"✅ Saved: {output_path}")
        else:
            print("❌ No valid image data received.")
    else:
        print(f"❌ Request failed with status code: {response.status_code}")
except Exception as e:
    print(f"❌ Request error: {e}")

# Print elapsed time
print(f"⏱️ Elapsed time: {time.time() - start_time:.2f} seconds")
